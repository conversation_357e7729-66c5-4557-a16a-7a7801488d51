{% extends "base.html" %}

{% block title %}تسجيل إنتاج حليب جديد - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus me-2"></i>
                تسجيل إنتاج حليب جديد
            </h2>
            <p class="text-muted mb-0">إدخال بيانات إنتاج الحليب اليومي</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Add Production Form -->
<div class="content-card">
    <form method="POST" id="productionForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="cow_id" class="form-label">البقرة <span class="text-danger">*</span></label>
                    <select class="form-select" id="cow_id" name="cow_id" required>
                        <option value="">اختر البقرة</option>
                        {% for cow in cows %}
                        <option value="{{ cow.id }}" {% if request.args.get('cow_id') == cow.id|string %}selected{% endif %}>
                            {{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="production_date" class="form-label">تاريخ الإنتاج <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="production_date" name="production_date" required>
                </div>
                
                <div class="mb-3">
                    <label for="milker_name" class="form-label">اسم الحلاب</label>
                    <input type="text" class="form-control" id="milker_name" name="milker_name">
                </div>
            </div>
            
            <!-- Production Quantities -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-glass-whiskey me-2"></i>
                    كميات الإنتاج
                </h5>
                
                <div class="mb-3">
                    <label for="morning_quantity_liters" class="form-label">كمية الصباح (لتر)</label>
                    <input type="number" step="0.1" class="form-control" id="morning_quantity_liters" 
                           name="morning_quantity_liters" min="0" value="0">
                </div>
                
                <div class="mb-3">
                    <label for="evening_quantity_liters" class="form-label">كمية المساء (لتر)</label>
                    <input type="number" step="0.1" class="form-control" id="evening_quantity_liters" 
                           name="evening_quantity_liters" min="0" value="0">
                </div>
                
                <div class="mb-3">
                    <label for="total_quantity" class="form-label">إجمالي الكمية (لتر)</label>
                    <input type="number" step="0.1" class="form-control" id="total_quantity" 
                           name="total_quantity" readonly style="background-color: #f8f9fa;">
                    <div class="form-text">يتم حسابها تلقائياً</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Quality Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-award me-2"></i>
                    معلومات الجودة
                </h5>
                
                <div class="mb-3">
                    <label for="fat_percentage" class="form-label">نسبة الدهون (%)</label>
                    <input type="number" step="0.01" class="form-control" id="fat_percentage" 
                           name="fat_percentage" min="0" max="10">
                </div>
                
                <div class="mb-3">
                    <label for="protein_percentage" class="form-label">نسبة البروتين (%)</label>
                    <input type="number" step="0.01" class="form-control" id="protein_percentage" 
                           name="protein_percentage" min="0" max="10">
                </div>
                
                <div class="mb-3">
                    <label for="quality_grade" class="form-label">درجة الجودة</label>
                    <select class="form-select" id="quality_grade" name="quality_grade">
                        <option value="A">A - ممتاز</option>
                        <option value="B">B - جيد</option>
                        <option value="C">C - مقبول</option>
                    </select>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-thermometer-half me-2"></i>
                    معلومات إضافية
                </h5>
                
                <div class="mb-3">
                    <label for="temperature_celsius" class="form-label">درجة الحرارة (°م)</label>
                    <input type="number" step="0.1" class="form-control" id="temperature_celsius" 
                           name="temperature_celsius" min="0" max="50">
                </div>
                
                <div class="mb-3">
                    <label for="somatic_cell_count" class="form-label">عدد الخلايا الجسدية</label>
                    <input type="number" class="form-control" id="somatic_cell_count" 
                           name="somatic_cell_count" min="0">
                    <div class="form-text">عدد الخلايا الجسدية لكل مل</div>
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="mb-4">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="3" 
                      placeholder="أي ملاحظات حول جودة الحليب أو ظروف الحلب"></textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ الإنتاج
            </button>
        </div>
    </form>
</div>

<!-- Quick Stats Card -->
{% if cows %}
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-chart-bar me-2"></i>
        الأبقار النشطة
    </h5>
    <div class="row">
        {% for cow in cows[:6] %}
        <div class="col-md-4 col-sm-6 mb-2">
            <div class="d-flex align-items-center p-2 border rounded">
                <div class="me-2">
                    <i class="fas fa-cow text-primary"></i>
                </div>
                <div>
                    <strong>{{ cow.tag_number }}</strong><br>
                    <small class="text-muted">{{ cow.name or 'بدون اسم' }}</small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% if cows|length > 6 %}
    <div class="text-center mt-2">
        <small class="text-muted">و {{ cows|length - 6 }} بقرة أخرى</small>
    </div>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('production_date').value = today;
    
    // Calculate total quantity automatically
    function calculateTotal() {
        const morning = parseFloat(document.getElementById('morning_quantity_liters').value) || 0;
        const evening = parseFloat(document.getElementById('evening_quantity_liters').value) || 0;
        const total = morning + evening;
        document.getElementById('total_quantity').value = total.toFixed(1);
    }
    
    // Add event listeners for quantity inputs
    document.getElementById('morning_quantity_liters').addEventListener('input', calculateTotal);
    document.getElementById('evening_quantity_liters').addEventListener('input', calculateTotal);
    
    // Initial calculation
    calculateTotal();
    
    // Form validation
    document.getElementById('productionForm').addEventListener('submit', function(e) {
        const total = parseFloat(document.getElementById('total_quantity').value) || 0;
        if (total <= 0) {
            alert('يجب أن تكون كمية الإنتاج أكبر من صفر');
            e.preventDefault();
            return false;
        }
        
        const productionDate = new Date(document.getElementById('production_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (productionDate > today) {
            alert('لا يمكن تسجيل إنتاج لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}

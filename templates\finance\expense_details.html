{% extends "base.html" %}

{% block title %}تفاصيل المصروف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-minus-circle me-2"></i>
                تفاصيل المصروف
            </h2>
            <p class="text-muted mb-0">{{ expense.description[:50] }}...</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('edit_expense', expense_id=expense.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Expense Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الفئة:</strong></div>
                <div class="col-sm-8">
                    {% if expense.category == 'feed' %}
                        <span class="badge bg-success">علف</span>
                    {% elif expense.category == 'veterinary' %}
                        <span class="badge bg-danger">بيطري</span>
                    {% elif expense.category == 'labor' %}
                        <span class="badge bg-primary">عمالة</span>
                    {% elif expense.category == 'utilities' %}
                        <span class="badge bg-warning">مرافق</span>
                    {% elif expense.category == 'maintenance' %}
                        <span class="badge bg-info">صيانة</span>
                    {% elif expense.category == 'equipment' %}
                        <span class="badge bg-dark">معدات</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ expense.category }}</span>
                    {% endif %}
                </div>
            </div>
            
            {% if expense.subcategory %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الفئة الفرعية:</strong></div>
                <div class="col-sm-8">{{ expense.subcategory }}</div>
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التاريخ:</strong></div>
                <div class="col-sm-8">{{ expense.expense_date.strftime('%Y-%m-%d') }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الوصف:</strong></div>
                <div class="col-sm-8">{{ expense.description }}</div>
            </div>
        </div>
    </div>
    
    <!-- Financial Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-money-bill-wave me-2"></i>
                المعلومات المالية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>المبلغ الإجمالي:</strong></div>
                <div class="col-sm-8">
                    <span class="text-danger fw-bold fs-5">{{ "%.2f"|format(expense.amount_jod) }} د.أ</span>
                </div>
            </div>
            
            {% if expense.quantity %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الكمية:</strong></div>
                <div class="col-sm-8">
                    {{ "%.1f"|format(expense.quantity) }}
                    {% if expense.unit %}{{ expense.unit }}{% endif %}
                </div>
            </div>
            {% endif %}
            
            {% if expense.unit_price_jod %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>سعر الوحدة:</strong></div>
                <div class="col-sm-8">{{ "%.3f"|format(expense.unit_price_jod) }} د.أ</div>
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>طريقة الدفع:</strong></div>
                <div class="col-sm-8">
                    {% if expense.payment_method == 'cash' %}
                        <span class="badge bg-success">نقدي</span>
                    {% elif expense.payment_method == 'bank_transfer' %}
                        <span class="badge bg-primary">تحويل بنكي</span>
                    {% elif expense.payment_method == 'check' %}
                        <span class="badge bg-warning">شيك</span>
                    {% elif expense.payment_method == 'credit_card' %}
                        <span class="badge bg-info">بطاقة ائتمان</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ expense.payment_method }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Supplier Information -->
{% if expense.supplier_name or expense.invoice_number %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-truck me-2"></i>
                معلومات المورد
            </h5>
            
            {% if expense.supplier_name %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>اسم المورد:</strong></div>
                <div class="col-sm-8">{{ expense.supplier_name }}</div>
            </div>
            {% endif %}
            
            {% if expense.invoice_number %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>رقم الفاتورة:</strong></div>
                <div class="col-sm-8">{{ expense.invoice_number }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Additional Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-clock me-2"></i>
                معلومات إضافية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>تاريخ الإنشاء:</strong></div>
                <div class="col-sm-8">{{ expense.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
            </div>
            
            {% if expense.updated_at and expense.updated_at != expense.created_at %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>آخر تحديث:</strong></div>
                <div class="col-sm-8">{{ expense.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
            </div>
            {% endif %}
            
            {% if expense.created_by_user %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>أنشأ بواسطة:</strong></div>
                <div class="col-sm-8">{{ expense.created_by_user.username }}</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Notes -->
{% if expense.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ expense.notes }}</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Cost Analysis -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>
                تحليل التكلفة
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-calendar-day text-primary fa-2x mb-2"></i>
                        <h6>تكلفة يومية</h6>
                        <p class="mb-0 text-primary fw-bold">{{ "%.2f"|format(expense.amount_jod) }} د.أ</p>
                    </div>
                </div>
                
                {% if expense.quantity and expense.unit %}
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-weight text-success fa-2x mb-2"></i>
                        <h6>تكلفة لكل {{ expense.unit }}</h6>
                        <p class="mb-0 text-success fw-bold">{{ "%.3f"|format(expense.unit_price_jod or 0) }} د.أ</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-percentage text-warning fa-2x mb-2"></i>
                        <h6>نسبة من الفئة</h6>
                        <p class="mb-0 text-warning fw-bold">--%</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-chart-line text-info fa-2x mb-2"></i>
                        <h6>الاتجاه الشهري</h6>
                        <p class="mb-0 text-info fw-bold">--%</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                <a href="{{ url_for('edit_expense', expense_id=expense.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل المصروف
                </a>
                <button class="btn btn-success me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة مصروف جديد
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .page-header .col-auto, .row:last-child {
        display: none !important;
    }
    .content-card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

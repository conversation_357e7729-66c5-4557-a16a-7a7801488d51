{% extends "base.html" %}

{% block title %}تعديل سجل الإنتاج - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل سجل إنتاج الحليب
            </h2>
            <p class="text-muted mb-0">تحديث بيانات إنتاج {{ production.production_date.strftime('%Y-%m-%d') }}</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Edit Production Form -->
<div class="content-card">
    <form method="POST">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="cow_id" class="form-label">البقرة <span class="text-danger">*</span></label>
                    <select class="form-select" id="cow_id" name="cow_id" required>
                        <option value="">اختر البقرة</option>
                        {% for cow in cows %}
                        <option value="{{ cow.id }}" {% if production.cow_id == cow.id %}selected{% endif %}>
                            {{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="production_date" class="form-label">تاريخ الإنتاج <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="production_date" name="production_date" 
                           value="{{ production.production_date.strftime('%Y-%m-%d') }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="milker_name" class="form-label">اسم الحالب</label>
                    <input type="text" class="form-control" id="milker_name" name="milker_name" 
                           value="{{ production.milker_name or '' }}" placeholder="اسم الشخص الذي قام بالحلب">
                </div>
            </div>
            
            <!-- Production Quantities -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-tint me-2"></i>
                    كميات الإنتاج
                </h5>
                
                <div class="mb-3">
                    <label for="morning_quantity_liters" class="form-label">كمية الصباح (لتر) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" class="form-control" id="morning_quantity_liters" 
                           name="morning_quantity_liters" value="{{ production.morning_quantity_liters or 0 }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="evening_quantity_liters" class="form-label">كمية المساء (لتر) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" class="form-control" id="evening_quantity_liters" 
                           name="evening_quantity_liters" value="{{ production.evening_quantity_liters or 0 }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="total_quantity" class="form-label">الإجمالي (لتر)</label>
                    <input type="number" step="0.1" class="form-control" id="total_quantity" 
                           value="{{ production.total_quantity_liters }}" readonly>
                    <div class="form-text">يتم الحساب تلقائياً</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Quality Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-star me-2"></i>
                    معلومات الجودة
                </h5>
                
                <div class="mb-3">
                    <label for="fat_percentage" class="form-label">نسبة الدهون (%)</label>
                    <input type="number" step="0.1" min="0" max="10" class="form-control" 
                           id="fat_percentage" name="fat_percentage" 
                           value="{{ production.fat_percentage or '' }}" placeholder="3.5">
                </div>
                
                <div class="mb-3">
                    <label for="protein_percentage" class="form-label">نسبة البروتين (%)</label>
                    <input type="number" step="0.1" min="0" max="10" class="form-control" 
                           id="protein_percentage" name="protein_percentage" 
                           value="{{ production.protein_percentage or '' }}" placeholder="3.2">
                </div>
                
                <div class="mb-3">
                    <label for="quality_grade" class="form-label">درجة الجودة</label>
                    <select class="form-select" id="quality_grade" name="quality_grade">
                        <option value="A" {% if production.quality_grade == 'A' %}selected{% endif %}>A - ممتاز</option>
                        <option value="B" {% if production.quality_grade == 'B' %}selected{% endif %}>B - جيد جداً</option>
                        <option value="C" {% if production.quality_grade == 'C' %}selected{% endif %}>C - جيد</option>
                        <option value="D" {% if production.quality_grade == 'D' %}selected{% endif %}>D - مقبول</option>
                    </select>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-thermometer-half me-2"></i>
                    معلومات إضافية
                </h5>
                
                <div class="mb-3">
                    <label for="temperature_celsius" class="form-label">درجة الحرارة (°م)</label>
                    <input type="number" step="0.1" class="form-control" id="temperature_celsius" 
                           name="temperature_celsius" value="{{ production.temperature_celsius or '' }}" 
                           placeholder="4.0">
                </div>
                
                <div class="mb-3">
                    <label for="somatic_cell_count" class="form-label">عدد الخلايا الجسدية</label>
                    <input type="number" class="form-control" id="somatic_cell_count" 
                           name="somatic_cell_count" value="{{ production.somatic_cell_count or '' }}" 
                           placeholder="200000">
                    <div class="form-text">أقل من 200,000 = جودة عالية</div>
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="mb-4">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="3" 
                      placeholder="أي ملاحظات حول جودة الحليب أو ظروف الحلب">{{ production.notes or '' }}</textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total quantity automatically
    function calculateTotal() {
        const morning = parseFloat(document.getElementById('morning_quantity_liters').value) || 0;
        const evening = parseFloat(document.getElementById('evening_quantity_liters').value) || 0;
        const total = morning + evening;
        document.getElementById('total_quantity').value = total.toFixed(1);
    }
    
    // Add event listeners
    document.getElementById('morning_quantity_liters').addEventListener('input', calculateTotal);
    document.getElementById('evening_quantity_liters').addEventListener('input', calculateTotal);
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const morning = parseFloat(document.getElementById('morning_quantity_liters').value) || 0;
        const evening = parseFloat(document.getElementById('evening_quantity_liters').value) || 0;
        const total = morning + evening;
        
        if (total <= 0) {
            alert('يجب إدخال كمية إنتاج صحيحة');
            e.preventDefault();
            return false;
        }
        
        if (total > 50) {
            if (!confirm('كمية الإنتاج عالية جداً (' + total + ' لتر). هل أنت متأكد؟')) {
                e.preventDefault();
                return false;
            }
        }
        
        const productionDate = new Date(document.getElementById('production_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (productionDate > today) {
            alert('لا يمكن تسجيل إنتاج لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
    });
    
    // Calculate total on page load
    calculateTotal();
});
</script>
{% endblock %}

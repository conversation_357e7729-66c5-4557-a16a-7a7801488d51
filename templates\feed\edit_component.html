{% extends "base.html" %}

{% block title %}تعديل مكون العلف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل مكون العلف
            </h2>
            <p class="text-muted mb-0">تحديث بيانات {{ component.name }}</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Edit Component Form -->
<div class="content-card">
    <form method="POST">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="name" class="form-label">الاسم العربي <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" 
                           value="{{ component.name }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="name_english" class="form-label">الاسم الإنجليزي</label>
                    <input type="text" class="form-control" id="name_english" name="name_english" 
                           value="{{ component.name_english or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="concentrate" {% if component.category == 'concentrate' %}selected{% endif %}>مركزات</option>
                        <option value="roughage" {% if component.category == 'roughage' %}selected{% endif %}>أعلاف خشنة</option>
                        <option value="supplement" {% if component.category == 'supplement' %}selected{% endif %}>مكملات غذائية</option>
                        <option value="mineral" {% if component.category == 'mineral' %}selected{% endif %}>أملاح معدنية</option>
                        <option value="vitamin" {% if component.category == 'vitamin' %}selected{% endif %}>فيتامينات</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="cost_per_kg_jod" class="form-label">التكلفة لكل كيلو (د.أ) <span class="text-danger">*</span></label>
                    <input type="number" step="0.001" class="form-control" id="cost_per_kg_jod" 
                           name="cost_per_kg_jod" value="{{ component.cost_per_kg_jod }}" required>
                </div>
            </div>
            
            <!-- Nutritional Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    القيم الغذائية
                </h5>
                
                <div class="mb-3">
                    <label for="protein_percentage" class="form-label">نسبة البروتين (%) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" min="0" max="100" class="form-control" 
                           id="protein_percentage" name="protein_percentage" 
                           value="{{ component.protein_percentage }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="energy_mcal_kg" class="form-label">الطاقة (ميجا كالوري/كغ) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" min="0" max="10" class="form-control" 
                           id="energy_mcal_kg" name="energy_mcal_kg" 
                           value="{{ component.energy_mcal_kg }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="fiber_percentage" class="form-label">نسبة الألياف (%) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" min="0" max="100" class="form-control" 
                           id="fiber_percentage" name="fiber_percentage" 
                           value="{{ component.fiber_percentage }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="fat_percentage" class="form-label">نسبة الدهون (%)</label>
                    <input type="number" step="0.1" min="0" max="100" class="form-control" 
                           id="fat_percentage" name="fat_percentage" 
                           value="{{ component.fat_percentage or 0 }}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Additional Nutritional Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-flask me-2"></i>
                    معلومات غذائية إضافية
                </h5>
                
                <div class="mb-3">
                    <label for="ash_percentage" class="form-label">نسبة الرماد (%)</label>
                    <input type="number" step="0.1" min="0" max="100" class="form-control" 
                           id="ash_percentage" name="ash_percentage" 
                           value="{{ component.ash_percentage or 0 }}">
                </div>
                
                <div class="mb-3">
                    <label for="moisture_percentage" class="form-label">نسبة الرطوبة (%)</label>
                    <input type="number" step="0.1" min="0" max="100" class="form-control" 
                           id="moisture_percentage" name="moisture_percentage" 
                           value="{{ component.moisture_percentage or 0 }}">
                </div>
                
                <div class="mb-3">
                    <label for="calcium_percentage" class="form-label">نسبة الكالسيوم (%)</label>
                    <input type="number" step="0.01" min="0" max="10" class="form-control" 
                           id="calcium_percentage" name="calcium_percentage" 
                           value="{{ component.calcium_percentage or 0 }}">
                </div>
                
                <div class="mb-3">
                    <label for="phosphorus_percentage" class="form-label">نسبة الفوسفور (%)</label>
                    <input type="number" step="0.01" min="0" max="10" class="form-control" 
                           id="phosphorus_percentage" name="phosphorus_percentage" 
                           value="{{ component.phosphorus_percentage or 0 }}">
                </div>
            </div>
            
            <!-- Supplier and Storage Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-truck me-2"></i>
                    معلومات المورد والتخزين
                </h5>
                
                <div class="mb-3">
                    <label for="supplier" class="form-label">المورد</label>
                    <input type="text" class="form-control" id="supplier" name="supplier" 
                           value="{{ component.supplier or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="supplier_phone" class="form-label">هاتف المورد</label>
                    <input type="text" class="form-control" id="supplier_phone" name="supplier_phone" 
                           value="{{ component.supplier_phone or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="shelf_life_days" class="form-label">مدة الصلاحية (أيام)</label>
                    <input type="number" min="1" class="form-control" id="shelf_life_days" 
                           name="shelf_life_days" value="{{ component.shelf_life_days or 30 }}">
                </div>
                
                <div class="mb-3">
                    <label for="storage_requirements" class="form-label">متطلبات التخزين</label>
                    <textarea class="form-control" id="storage_requirements" name="storage_requirements" 
                              rows="2" placeholder="مثل: مكان جاف وبارد">{{ component.storage_requirements or '' }}</textarea>
                </div>
            </div>
        </div>
        
        <!-- Description -->
        <div class="mb-4">
            <label for="description" class="form-label">الوصف</label>
            <textarea class="form-control" id="description" name="description" rows="3" 
                      placeholder="وصف مفصل عن المكون وفوائده">{{ component.description or '' }}</textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const protein = parseFloat(document.getElementById('protein_percentage').value) || 0;
        const fiber = parseFloat(document.getElementById('fiber_percentage').value) || 0;
        const fat = parseFloat(document.getElementById('fat_percentage').value) || 0;
        const ash = parseFloat(document.getElementById('ash_percentage').value) || 0;
        const moisture = parseFloat(document.getElementById('moisture_percentage').value) || 0;
        
        const total = protein + fiber + fat + ash + moisture;
        
        if (total > 100) {
            alert('مجموع النسب الغذائية لا يمكن أن يتجاوز 100%');
            e.preventDefault();
            return false;
        }
        
        const cost = parseFloat(document.getElementById('cost_per_kg_jod').value) || 0;
        if (cost <= 0) {
            alert('يجب إدخال تكلفة صحيحة');
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-suggest based on category
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const nameInput = document.getElementById('name');
        
        if (category === 'concentrate') {
            // Suggest typical concentrate values
            document.getElementById('protein_percentage').placeholder = '12-25';
            document.getElementById('energy_mcal_kg').placeholder = '2.5-3.5';
            document.getElementById('fiber_percentage').placeholder = '3-15';
        } else if (category === 'roughage') {
            // Suggest typical roughage values
            document.getElementById('protein_percentage').placeholder = '4-15';
            document.getElementById('energy_mcal_kg').placeholder = '1.5-2.5';
            document.getElementById('fiber_percentage').placeholder = '25-45';
        } else if (category === 'supplement') {
            // Suggest typical supplement values
            document.getElementById('protein_percentage').placeholder = '20-50';
            document.getElementById('energy_mcal_kg').placeholder = '2.0-4.0';
            document.getElementById('fiber_percentage').placeholder = '0-10';
        }
    });
});
</script>
{% endblock %}

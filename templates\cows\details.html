{% extends "base.html" %}

{% block title %}تفاصيل البقرة {{ cow.tag_number }} - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-cow me-2"></i>
                البقرة رقم {{ cow.tag_number }}
                {% if cow.name %}
                    - {{ cow.name }}
                {% endif %}
            </h2>
            <p class="text-muted mb-0">تفاصيل شاملة عن البقرة وأدائها</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('edit_cow', cow_id=cow.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="{{ url_for('cows_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Production Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">
                {% if today_production %}
                    {{ "%.1f"|format(today_production.total_quantity_liters) }}
                {% else %}
                    0.0
                {% endif %}
            </div>
            <div class="label">إنتاج اليوم (لتر)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.1f"|format(week_production) }}</div>
            <div class="label">إنتاج الأسبوع (لتر)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.1f"|format(month_production) }}</div>
            <div class="label">إنتاج الشهر (لتر)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.1f"|format(avg_production) }}</div>
            <div class="label">متوسط يومي (لتر)</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            <table class="table table-borderless">
                <tr>
                    <td><strong>رقم الأذن:</strong></td>
                    <td>{{ cow.tag_number }}</td>
                </tr>
                <tr>
                    <td><strong>الاسم:</strong></td>
                    <td>{{ cow.name or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>السلالة:</strong></td>
                    <td>{{ cow.breed or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>الحالة:</strong></td>
                    <td>
                        {% if cow.status == 'active' %}
                            <span class="badge bg-success">نشطة</span>
                        {% elif cow.status == 'pregnant' %}
                            <span class="badge bg-info">حامل</span>
                        {% elif cow.status == 'dry' %}
                            <span class="badge bg-warning">جافة</span>
                        {% elif cow.status == 'sick' %}
                            <span class="badge bg-danger">مريضة</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ cow.status }}</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>تاريخ الولادة:</strong></td>
                    <td>
                        {% if cow.birth_date %}
                            {{ cow.birth_date.strftime('%Y-%m-%d') }}
                            ({{ cow.birth_date | age_from_birth }})
                        {% else %}
                            -
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>تاريخ الشراء:</strong></td>
                    <td>{{ cow.purchase_date.strftime('%Y-%m-%d') if cow.purchase_date else '-' }}</td>
                </tr>
            </table>
        </div>
    </div>
    
    <!-- Physical Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-weight me-2"></i>
                المعلومات الجسدية والمالية
            </h5>
            <table class="table table-borderless">
                <tr>
                    <td><strong>الوزن الحالي:</strong></td>
                    <td>{{ cow.current_weight_kg }} كغ</td>
                </tr>
                <tr>
                    <td><strong>وزن الشراء:</strong></td>
                    <td>{{ cow.purchase_weight_kg }} كغ</td>
                </tr>
                <tr>
                    <td><strong>سعر الشراء:</strong></td>
                    <td>{{ cow.purchase_price_jod }} دينار أردني</td>
                </tr>
                <tr>
                    <td><strong>درجة الحالة الجسدية:</strong></td>
                    <td>{{ cow.body_condition_score or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>عدد الولادات:</strong></td>
                    <td>{{ cow.total_calvings or 0 }}</td>
                </tr>
                <tr>
                    <td><strong>آخر تلقيح:</strong></td>
                    <td>{{ cow.last_breeding_date.strftime('%Y-%m-%d') if cow.last_breeding_date else '-' }}</td>
                </tr>
            </table>
        </div>
    </div>
</div>

<!-- Genetic Information -->
{% if cow.sire_name or cow.dam_name %}
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-dna me-2"></i>
        المعلومات الوراثية
    </h5>
    <div class="row">
        <div class="col-md-6">
            <strong>اسم الأب:</strong> {{ cow.sire_name or '-' }}
        </div>
        <div class="col-md-6">
            <strong>اسم الأم:</strong> {{ cow.dam_name or '-' }}
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Health Records -->
{% if recent_health_records %}
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-heartbeat me-2"></i>
        آخر السجلات الصحية
    </h5>
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>نوع السجل</th>
                    <th>التشخيص</th>
                    <th>العلاج</th>
                    <th>الطبيب البيطري</th>
                </tr>
            </thead>
            <tbody>
                {% for record in recent_health_records %}
                <tr>
                    <td>{{ record.record_date.strftime('%Y-%m-%d') }}</td>
                    <td>
                        {% if record.record_type == 'vaccination' %}
                            <span class="badge bg-primary">تطعيم</span>
                        {% elif record.record_type == 'treatment' %}
                            <span class="badge bg-warning">علاج</span>
                        {% elif record.record_type == 'checkup' %}
                            <span class="badge bg-info">فحص</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ record.record_type }}</span>
                        {% endif %}
                    </td>
                    <td>{{ record.condition_diagnosis or '-' }}</td>
                    <td>{{ record.treatment_given or '-' }}</td>
                    <td>{{ record.veterinarian_name or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Notes -->
{% if cow.notes %}
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-sticky-note me-2"></i>
        ملاحظات
    </h5>
    <p class="mb-0">{{ cow.notes }}</p>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-bolt me-2"></i>
        إجراءات سريعة
    </h5>
    <div class="row">
        <div class="col-md-4 mb-2">
            <a href="{{ url_for('add_milk_production') }}?cow_id={{ cow.id }}" class="btn btn-outline-primary w-100">
                <i class="fas fa-plus me-2"></i>
                تسجيل إنتاج حليب
            </a>
        </div>
        <div class="col-md-4 mb-2">
            <a href="{{ url_for('add_health_record') }}?cow_id={{ cow.id }}" class="btn btn-outline-success w-100">
                <i class="fas fa-plus me-2"></i>
                إضافة سجل صحي
            </a>
        </div>
        <div class="col-md-4 mb-2">
            <a href="{{ url_for('edit_cow', cow_id=cow.id) }}" class="btn btn-outline-warning w-100">
                <i class="fas fa-edit me-2"></i>
                تعديل البيانات
            </a>
        </div>
    </div>
</div>
{% endblock %}

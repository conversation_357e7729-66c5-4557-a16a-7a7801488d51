{% extends "base.html" %}

{% block title %}المصروفات - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-minus-circle me-2"></i>
                إدارة المصروفات
            </h2>
            <p class="text-muted mb-0">تتبع ومراقبة جميع مصروفات المزرعة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مصروف جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(total_expenses) }}</div>
            <div class="label">إجمالي المصروفات (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(month_expenses) }}</div>
            <div class="label">مصروفات الشهر (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(feed_expenses) }}</div>
            <div class="label">مصروفات العلف (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(veterinary_expenses) }}</div>
            <div class="label">مصروفات بيطرية (د.أ)</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="category" class="form-label">الفئة</label>
            <select class="form-select" id="category" name="category">
                <option value="">جميع الفئات</option>
                <option value="feed" {% if category_filter == 'feed' %}selected{% endif %}>علف</option>
                <option value="veterinary" {% if category_filter == 'veterinary' %}selected{% endif %}>بيطري</option>
                <option value="labor" {% if category_filter == 'labor' %}selected{% endif %}>عمالة</option>
                <option value="utilities" {% if category_filter == 'utilities' %}selected{% endif %}>مرافق</option>
                <option value="maintenance" {% if category_filter == 'maintenance' %}selected{% endif %}>صيانة</option>
                <option value="equipment" {% if category_filter == 'equipment' %}selected{% endif %}>معدات</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="date_from" class="form-label">من تاريخ</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
        </div>
        <div class="col-md-3">
            <label for="date_to" class="form-label">إلى تاريخ</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Expenses Table -->
<div class="content-card">
    {% if expenses.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>الوصف</th>
                        <th>المبلغ (د.أ)</th>
                        <th>الكمية</th>
                        <th>المورد</th>
                        <th>طريقة الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in expenses.items %}
                    <tr>
                        <td>
                            <strong>{{ expense.expense_date.strftime('%Y-%m-%d') }}</strong>
                        </td>
                        <td>
                            {% if expense.category == 'feed' %}
                                <span class="badge bg-success">علف</span>
                            {% elif expense.category == 'veterinary' %}
                                <span class="badge bg-danger">بيطري</span>
                            {% elif expense.category == 'labor' %}
                                <span class="badge bg-primary">عمالة</span>
                            {% elif expense.category == 'utilities' %}
                                <span class="badge bg-warning">مرافق</span>
                            {% elif expense.category == 'maintenance' %}
                                <span class="badge bg-info">صيانة</span>
                            {% elif expense.category == 'equipment' %}
                                <span class="badge bg-secondary">معدات</span>
                            {% else %}
                                <span class="badge bg-dark">{{ expense.category }}</span>
                            {% endif %}
                            {% if expense.subcategory %}
                                <br><small class="text-muted">{{ expense.subcategory }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {{ expense.description }}
                            {% if expense.invoice_number %}
                                <br><small class="text-muted">فاتورة: {{ expense.invoice_number }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <strong class="text-danger">{{ "%.2f"|format(expense.amount_jod) }}</strong>
                            {% if expense.unit_price_jod and expense.quantity %}
                                <br><small class="text-muted">{{ "%.2f"|format(expense.unit_price_jod) }} × {{ expense.quantity }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if expense.quantity %}
                                {{ expense.quantity }} {{ expense.unit or '' }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ expense.supplier_name or '-' }}</td>
                        <td>
                            {% if expense.payment_method == 'cash' %}
                                <span class="badge bg-success">نقدي</span>
                            {% elif expense.payment_method == 'bank_transfer' %}
                                <span class="badge bg-primary">تحويل بنكي</span>
                            {% elif expense.payment_method == 'check' %}
                                <span class="badge bg-warning">شيك</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ expense.payment_method }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('expense_details', expense_id=expense.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_expense', expense_id=expense.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ expense.description[:30] }}...', '{{ url_for('delete_expense', expense_id=expense.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if expenses.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if expenses.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('expenses_list', page=expenses.prev_num, category=category_filter, date_from=date_from, date_to=date_to) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in expenses.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != expenses.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('expenses_list', page=page_num, category=category_filter, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if expenses.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('expenses_list', page=expenses.next_num, category=category_filter, date_from=date_from, date_to=date_to) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-minus-circle fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مصروفات</h5>
            <p class="text-muted">ابدأ بإضافة أول مصروف</p>
            <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مصروف جديد
            </a>
        </div>
    {% endif %}
</div>

<!-- Expense Details Modal -->
<div class="modal fade" id="expenseDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المصروف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="expenseDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set default date range (current month)
document.addEventListener('DOMContentLoaded', function() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (!dateFromInput.value && !dateToInput.value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        
        dateFromInput.value = firstDay.toISOString().split('T')[0];
        dateToInput.value = today.toISOString().split('T')[0];
    }
});

function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف المصروف "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

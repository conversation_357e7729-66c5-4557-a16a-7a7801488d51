{% extends "base.html" %}

{% block title %}الإيرادات - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus-circle me-2"></i>
                إدارة الإيرادات
            </h2>
            <p class="text-muted mb-0">تتبع ومراقبة جميع إيرادات المزرعة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_revenue') }}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                إضافة إيراد جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(total_revenues) }}</div>
            <div class="label">إجمالي الإيرادات (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(month_revenues) }}</div>
            <div class="label">إيرادات الشهر (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(milk_revenues) }}</div>
            <div class="label">مبيعات الحليب (د.أ)</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.0f"|format(cow_revenues) }}</div>
            <div class="label">مبيعات الأبقار (د.أ)</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="source" class="form-label">مصدر الإيراد</label>
            <select class="form-select" id="source" name="source">
                <option value="">جميع المصادر</option>
                <option value="milk_sale" {% if source_filter == 'milk_sale' %}selected{% endif %}>بيع حليب</option>
                <option value="cow_sale" {% if source_filter == 'cow_sale' %}selected{% endif %}>بيع أبقار</option>
                <option value="calf_sale" {% if source_filter == 'calf_sale' %}selected{% endif %}>بيع عجول</option>
                <option value="manure_sale" {% if source_filter == 'manure_sale' %}selected{% endif %}>بيع سماد</option>
                <option value="other" {% if source_filter == 'other' %}selected{% endif %}>أخرى</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="date_from" class="form-label">من تاريخ</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
        </div>
        <div class="col-md-3">
            <label for="date_to" class="form-label">إلى تاريخ</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('revenues_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Revenues Table -->
<div class="content-card">
    {% if revenues.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المصدر</th>
                        <th>الوصف</th>
                        <th>المبلغ (د.أ)</th>
                        <th>الكمية</th>
                        <th>العميل</th>
                        <th>حالة الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for revenue in revenues.items %}
                    <tr>
                        <td>
                            <strong>{{ revenue.sale_date.strftime('%Y-%m-%d') }}</strong>
                        </td>
                        <td>
                            {% if revenue.source == 'milk_sale' %}
                                <span class="badge bg-primary">بيع حليب</span>
                            {% elif revenue.source == 'cow_sale' %}
                                <span class="badge bg-success">بيع أبقار</span>
                            {% elif revenue.source == 'calf_sale' %}
                                <span class="badge bg-info">بيع عجول</span>
                            {% elif revenue.source == 'manure_sale' %}
                                <span class="badge bg-warning">بيع سماد</span>
                            {% else %}
                                <span class="badge bg-secondary">أخرى</span>
                            {% endif %}
                        </td>
                        <td>{{ revenue.description }}</td>
                        <td>
                            <strong class="text-success">{{ "%.2f"|format(revenue.amount_jod) }}</strong>
                            {% if revenue.price_per_liter_jod and revenue.quantity_liters %}
                                <br><small class="text-muted">{{ "%.3f"|format(revenue.price_per_liter_jod) }} × {{ revenue.quantity_liters }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if revenue.quantity_liters %}
                                {{ revenue.quantity_liters }} لتر
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if revenue.customer_name %}
                                {{ revenue.customer_name }}
                                {% if revenue.customer_phone %}
                                    <br><small class="text-muted">{{ revenue.customer_phone }}</small>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if revenue.payment_status == 'paid' %}
                                <span class="badge bg-success">مدفوع</span>
                            {% elif revenue.payment_status == 'pending' %}
                                <span class="badge bg-warning">معلق</span>
                            {% elif revenue.payment_status == 'overdue' %}
                                <span class="badge bg-danger">متأخر</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ revenue.payment_status }}</span>
                            {% endif %}
                            <br>
                            {% if revenue.payment_method == 'cash' %}
                                <small class="text-muted">نقدي</small>
                            {% elif revenue.payment_method == 'bank_transfer' %}
                                <small class="text-muted">تحويل بنكي</small>
                            {% elif revenue.payment_method == 'check' %}
                                <small class="text-muted">شيك</small>
                            {% else %}
                                <small class="text-muted">{{ revenue.payment_method }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('revenue_details', revenue_id=revenue.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_revenue', revenue_id=revenue.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if revenue.payment_status != 'paid' %}
                                <button class="btn btn-outline-success" title="تحديث حالة الدفع" disabled>
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ revenue.description[:30] }}...', '{{ url_for('delete_revenue', revenue_id=revenue.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if revenues.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if revenues.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('revenues_list', page=revenues.prev_num, source=source_filter, date_from=date_from, date_to=date_to) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in revenues.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != revenues.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('revenues_list', page=page_num, source=source_filter, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if revenues.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('revenues_list', page=revenues.next_num, source=source_filter, date_from=date_from, date_to=date_to) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إيرادات</h5>
            <p class="text-muted">ابدأ بإضافة أول إيراد</p>
            <a href="{{ url_for('add_revenue') }}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                إضافة إيراد جديد
            </a>
        </div>
    {% endif %}
</div>

<!-- Revenue Details Modal -->
<div class="modal fade" id="revenueDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الإيراد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="revenueDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set default date range (current month)
document.addEventListener('DOMContentLoaded', function() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (!dateFromInput.value && !dateToInput.value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        
        dateFromInput.value = firstDay.toISOString().split('T')[0];
        dateToInput.value = today.toISOString().split('T')[0];
    }
});

function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف الإيراد "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

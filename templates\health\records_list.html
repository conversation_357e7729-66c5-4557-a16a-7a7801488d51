{% extends "base.html" %}

{% block title %}السجلات الصحية - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-heartbeat me-2"></i>
                السجلات الصحية
            </h2>
            <p class="text-muted mb-0">متابعة الحالة الصحية للأبقار</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_health_record') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة سجل صحي
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ total_records }}</div>
            <div class="label">إجمالي السجلات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ vaccination_records }}</div>
            <div class="label">تطعيمات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ treatment_records }}</div>
            <div class="label">علاجات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ checkup_records }}</div>
            <div class="label">فحوصات</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="cow_id" class="form-label">البقرة</label>
            <select class="form-select" id="cow_id" name="cow_id">
                <option value="">جميع الأبقار</option>
                {% for cow in cows %}
                <option value="{{ cow.id }}" {% if cow_filter == cow.id|string %}selected{% endif %}>
                    {{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label for="record_type" class="form-label">نوع السجل</label>
            <select class="form-select" id="record_type" name="record_type">
                <option value="">جميع الأنواع</option>
                <option value="vaccination" {% if record_type_filter == 'vaccination' %}selected{% endif %}>تطعيم</option>
                <option value="treatment" {% if record_type_filter == 'treatment' %}selected{% endif %}>علاج</option>
                <option value="checkup" {% if record_type_filter == 'checkup' %}selected{% endif %}>فحص</option>
                <option value="surgery" {% if record_type_filter == 'surgery' %}selected{% endif %}>جراحة</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="date_from" class="form-label">من تاريخ</label>
            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
        </div>
        <div class="col-md-2">
            <label for="date_to" class="form-label">إلى تاريخ</label>
            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('health_records_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Health Records Table -->
<div class="content-card">
    {% if records.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>البقرة</th>
                        <th>نوع السجل</th>
                        <th>التشخيص</th>
                        <th>العلاج</th>
                        <th>الطبيب البيطري</th>
                        <th>التكلفة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in records.items %}
                    <tr>
                        <td>
                            <strong>{{ record.record_date.strftime('%Y-%m-%d') }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-cow text-primary me-2"></i>
                                <div>
                                    <strong>{{ record.cow.tag_number }}</strong><br>
                                    <small class="text-muted">{{ record.cow.name or 'بدون اسم' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if record.record_type == 'vaccination' %}
                                <span class="badge bg-primary">تطعيم</span>
                            {% elif record.record_type == 'treatment' %}
                                <span class="badge bg-warning">علاج</span>
                            {% elif record.record_type == 'checkup' %}
                                <span class="badge bg-info">فحص</span>
                            {% elif record.record_type == 'surgery' %}
                                <span class="badge bg-danger">جراحة</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ record.record_type }}</span>
                            {% endif %}
                        </td>
                        <td>{{ record.condition_diagnosis or '-' }}</td>
                        <td>
                            {% if record.treatment_given %}
                                {{ record.treatment_given[:30] }}{% if record.treatment_given|length > 30 %}...{% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if record.veterinarian_name %}
                                {{ record.veterinarian_name }}
                                {% if record.veterinarian_phone %}
                                    <br><small class="text-muted">{{ record.veterinarian_phone }}</small>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if record.treatment_cost_jod %}
                                {{ "%.2f"|format(record.treatment_cost_jod) }} د.أ
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('health_record_details', record_id=record.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_health_record', record_id=record.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if record.follow_up_required %}
                                <button class="btn btn-outline-info" title="متابعة مطلوبة">
                                    <i class="fas fa-clock"></i>
                                </button>
                                {% endif %}
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('سجل صحي {{ record.record_date.strftime('%Y-%m-%d') }}', '{{ url_for('delete_health_record', record_id=record.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if records.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if records.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('health_records_list', page=records.prev_num, cow_id=cow_filter, record_type=record_type_filter, date_from=date_from, date_to=date_to) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in records.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != records.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('health_records_list', page=page_num, cow_id=cow_filter, record_type=record_type_filter, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if records.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('health_records_list', page=records.next_num, cow_id=cow_filter, record_type=record_type_filter, date_from=date_from, date_to=date_to) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد سجلات صحية</h5>
            <p class="text-muted">ابدأ بإضافة أول سجل صحي</p>
            <a href="{{ url_for('add_health_record') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة سجل صحي
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set default date range (last 30 days)
document.addEventListener('DOMContentLoaded', function() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (!dateFromInput.value && !dateToInput.value) {
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        dateFromInput.value = thirtyDaysAgo.toISOString().split('T')[0];
        dateToInput.value = today.toISOString().split('T')[0];
    }
});

function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

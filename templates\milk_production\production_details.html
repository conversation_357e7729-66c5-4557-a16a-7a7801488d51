{% extends "base.html" %}

{% block title %}تفاصيل إنتاج الحليب - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-tint me-2"></i>
                تفاصيل إنتاج الحليب
            </h2>
            <p class="text-muted mb-0">{{ production.production_date.strftime('%Y-%m-%d') }} - {{ production.cow.tag_number }} {{ production.cow.name or '' }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('edit_milk_production', production_id=production.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Production Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                معلومات الإنتاج
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>البقرة:</strong></div>
                <div class="col-sm-8">
                    <a href="{{ url_for('cow_details', cow_id=production.cow.id) }}" class="text-decoration-none">
                        {{ production.cow.tag_number }} - {{ production.cow.name or 'بدون اسم' }}
                    </a>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التاريخ:</strong></div>
                <div class="col-sm-8">{{ production.production_date.strftime('%Y-%m-%d') }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الحلابة الصباحية:</strong></div>
                <div class="col-sm-8">
                    <span class="badge bg-primary">{{ "%.1f"|format(production.morning_quantity_liters) }} لتر</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الحلابة المسائية:</strong></div>
                <div class="col-sm-8">
                    <span class="badge bg-info">{{ "%.1f"|format(production.evening_quantity_liters) }} لتر</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الإجمالي اليومي:</strong></div>
                <div class="col-sm-8">
                    <span class="badge bg-success fs-6">{{ "%.1f"|format(production.total_quantity_liters) }} لتر</span>
                </div>
            </div>
            
            {% if production.milker_name %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الحلاب:</strong></div>
                <div class="col-sm-8">{{ production.milker_name }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Quality Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-award me-2"></i>
                جودة الحليب
            </h5>
            
            {% if production.fat_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>نسبة الدهون:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(production.fat_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if production.protein_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>نسبة البروتين:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(production.protein_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if production.somatic_cell_count %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>عدد الخلايا الجسدية:</strong></div>
                <div class="col-sm-6">{{ "{:,}"|format(production.somatic_cell_count) }}</div>
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>درجة الجودة:</strong></div>
                <div class="col-sm-6">
                    {% if production.quality_grade == 'A' %}
                        <span class="badge bg-success">ممتاز (A)</span>
                    {% elif production.quality_grade == 'B' %}
                        <span class="badge bg-warning">جيد (B)</span>
                    {% elif production.quality_grade == 'C' %}
                        <span class="badge bg-danger">مقبول (C)</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ production.quality_grade }}</span>
                    {% endif %}
                </div>
            </div>
            
            {% if production.temperature_celsius %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>درجة الحرارة:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(production.temperature_celsius) }}°م</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Weekly Production Summary -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-line me-2"></i>
                ملخص الإنتاج الأسبوعي
            </h5>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-calendar-day text-primary fa-2x mb-2"></i>
                        <h6>إنتاج اليوم</h6>
                        <p class="mb-0 text-primary fw-bold">{{ "%.1f"|format(production.total_quantity_liters) }} لتر</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-chart-bar text-success fa-2x mb-2"></i>
                        <h6>متوسط الأسبوع</h6>
                        <p class="mb-0 text-success fw-bold">{{ "%.1f"|format(weekly_average) }} لتر</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-percentage text-info fa-2x mb-2"></i>
                        <h6>نسبة للمتوسط</h6>
                        {% set percentage = (production.total_quantity_liters / weekly_average * 100) if weekly_average > 0 else 0 %}
                        <p class="mb-0 text-info fw-bold">{{ "%.0f"|format(percentage) }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Productions -->
{% if recent_productions %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-history me-2"></i>
                آخر سجلات الإنتاج لنفس البقرة
            </h5>
            
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الصباح</th>
                            <th>المساء</th>
                            <th>الإجمالي</th>
                            <th>الجودة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prod in recent_productions %}
                        <tr>
                            <td>{{ prod.production_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ "%.1f"|format(prod.morning_quantity_liters) }} لتر</td>
                            <td>{{ "%.1f"|format(prod.evening_quantity_liters) }} لتر</td>
                            <td><strong>{{ "%.1f"|format(prod.total_quantity_liters) }} لتر</strong></td>
                            <td>
                                {% if prod.quality_grade == 'A' %}
                                    <span class="badge bg-success">A</span>
                                {% elif prod.quality_grade == 'B' %}
                                    <span class="badge bg-warning">B</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ prod.quality_grade }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('milk_production_details', production_id=prod.id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Notes -->
{% if production.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ production.notes }}</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                <a href="{{ url_for('edit_milk_production', production_id=production.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل السجل
                </a>
                <button class="btn btn-success me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <a href="{{ url_for('add_milk_production') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة سجل جديد
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .page-header .col-auto, .row:last-child {
        display: none !important;
    }
    .content-card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

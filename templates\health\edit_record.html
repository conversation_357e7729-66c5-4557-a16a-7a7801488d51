{% extends "base.html" %}

{% block title %}تعديل السجل الصحي - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل السجل الصحي
            </h2>
            <p class="text-muted mb-0">تحديث سجل {{ record.record_date.strftime('%Y-%m-%d') }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('health_record_details', record_id=record.id) }}" class="btn btn-outline-info">
                    <i class="fas fa-eye me-1"></i>
                    عرض التفاصيل
                </a>
                <a href="{{ url_for('health_records_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Edit Health Record Form -->
<div class="content-card">
    <form method="POST" id="healthRecordForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="cow_id" class="form-label">البقرة <span class="text-danger">*</span></label>
                    <select class="form-select" id="cow_id" name="cow_id" required>
                        <option value="">اختر البقرة</option>
                        {% for cow in cows %}
                        <option value="{{ cow.id }}" {% if record.cow_id == cow.id %}selected{% endif %}>
                            {{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="record_date" class="form-label">تاريخ السجل <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="record_date" name="record_date" 
                           value="{{ record.record_date.strftime('%Y-%m-%d') }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="record_type" class="form-label">نوع السجل <span class="text-danger">*</span></label>
                    <select class="form-select" id="record_type" name="record_type" required>
                        <option value="">اختر نوع السجل</option>
                        <option value="vaccination" {% if record.record_type == 'vaccination' %}selected{% endif %}>تطعيم</option>
                        <option value="treatment" {% if record.record_type == 'treatment' %}selected{% endif %}>علاج</option>
                        <option value="checkup" {% if record.record_type == 'checkup' %}selected{% endif %}>فحص دوري</option>
                        <option value="surgery" {% if record.record_type == 'surgery' %}selected{% endif %}>جراحة</option>
                        <option value="emergency" {% if record.record_type == 'emergency' %}selected{% endif %}>حالة طوارئ</option>
                    </select>
                </div>
            </div>
            
            <!-- Medical Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-stethoscope me-2"></i>
                    المعلومات الطبية
                </h5>
                
                <div class="mb-3">
                    <label for="condition_diagnosis" class="form-label">التشخيص/الحالة</label>
                    <input type="text" class="form-control" id="condition_diagnosis" name="condition_diagnosis" 
                           value="{{ record.condition_diagnosis or '' }}"
                           placeholder="مثل: التهاب الضرع، تطعيم ضد الحمى القلاعية">
                </div>
                
                <div class="mb-3">
                    <label for="treatment_given" class="form-label">العلاج المُقدم</label>
                    <textarea class="form-control" id="treatment_given" name="treatment_given" rows="2" 
                              placeholder="وصف العلاج أو الإجراء المتخذ">{{ record.treatment_given or '' }}</textarea>
                </div>
                
                <div class="mb-3">
                    <label for="treatment_cost_jod" class="form-label">التكلفة (دينار أردني)</label>
                    <input type="number" step="0.01" class="form-control" id="treatment_cost_jod" name="treatment_cost_jod" 
                           value="{{ record.treatment_cost_jod or '' }}"
                           placeholder="تكلفة العلاج أو الفحص">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Medication Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-pills me-2"></i>
                    معلومات الدواء
                </h5>
                
                <div class="mb-3">
                    <label for="medication_used" class="form-label">اسم الدواء</label>
                    <input type="text" class="form-control" id="medication_used" name="medication_used" 
                           value="{{ record.medication_used or '' }}"
                           placeholder="اسم الدواء أو اللقاح">
                </div>
                
                <div class="mb-3">
                    <label for="dosage" class="form-label">الجرعة</label>
                    <input type="text" class="form-control" id="dosage" name="dosage" 
                           value="{{ record.dosage or '' }}"
                           placeholder="مثل: 10 مل، 2 حبة، 5 سم³">
                </div>
                
                <div class="mb-3">
                    <label for="veterinarian_name" class="form-label">اسم الطبيب البيطري</label>
                    <input type="text" class="form-control" id="veterinarian_name" name="veterinarian_name" 
                           value="{{ record.veterinarian_name or '' }}"
                           placeholder="اسم الطبيب البيطري">
                </div>
            </div>
            
            <!-- Follow-up Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-calendar-check me-2"></i>
                    معلومات المتابعة
                </h5>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="follow_up_required" name="follow_up_required"
                               {% if record.follow_up_required %}checked{% endif %}>
                        <label class="form-check-label" for="follow_up_required">
                            متابعة مطلوبة
                        </label>
                    </div>
                </div>
                
                <div class="mb-3" id="followUpDateContainer" {% if not record.follow_up_required %}style="display: none;"{% endif %}>
                    <label for="follow_up_date" class="form-label">تاريخ المتابعة</label>
                    <input type="date" class="form-control" id="follow_up_date" name="follow_up_date"
                           value="{{ record.follow_up_date.strftime('%Y-%m-%d') if record.follow_up_date else '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية حول الحالة أو العلاج">{{ record.notes or '' }}</textarea>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('health_record_details', record_id=record.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>

<!-- Quick Reference Card -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-lightbulb me-2"></i>
        مرجع سريع - أنواع السجلات الصحية
    </h5>
    <div class="row">
        <div class="col-md-3">
            <h6><span class="badge bg-primary">تطعيم</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• تطعيمات دورية</li>
                <li>• لقاحات وقائية</li>
                <li>• تطعيمات موسمية</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-warning">علاج</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• علاج الأمراض</li>
                <li>• المضادات الحيوية</li>
                <li>• العلاجات الطبية</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-info">فحص دوري</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• فحص الحمل</li>
                <li>• فحص الضرع</li>
                <li>• فحص عام</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-danger">جراحة</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• عمليات جراحية</li>
                <li>• إجراءات طبية</li>
                <li>• حالات طوارئ</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide follow-up date based on checkbox
    const followUpCheckbox = document.getElementById('follow_up_required');
    const followUpContainer = document.getElementById('followUpDateContainer');
    
    followUpCheckbox.addEventListener('change', function() {
        if (this.checked) {
            followUpContainer.style.display = 'block';
            // Set default follow-up date to 7 days from record date if not set
            const followUpDateInput = document.getElementById('follow_up_date');
            if (!followUpDateInput.value) {
                const recordDate = new Date(document.getElementById('record_date').value);
                if (recordDate) {
                    recordDate.setDate(recordDate.getDate() + 7);
                    followUpDateInput.value = recordDate.toISOString().split('T')[0];
                }
            }
        } else {
            followUpContainer.style.display = 'none';
        }
    });
    
    // Auto-suggest based on record type
    document.getElementById('record_type').addEventListener('change', function() {
        const recordType = this.value;
        const diagnosisInput = document.getElementById('condition_diagnosis');
        const treatmentInput = document.getElementById('treatment_given');
        
        if (recordType === 'vaccination') {
            diagnosisInput.placeholder = 'مثل: تطعيم ضد الحمى القلاعية';
            treatmentInput.placeholder = 'مثل: حقن لقاح تحت الجلد';
        } else if (recordType === 'treatment') {
            diagnosisInput.placeholder = 'مثل: التهاب الضرع';
            treatmentInput.placeholder = 'مثل: مضاد حيوي + مضاد التهاب';
        } else if (recordType === 'checkup') {
            diagnosisInput.placeholder = 'مثل: فحص الحمل';
            treatmentInput.placeholder = 'مثل: فحص بالموجات فوق الصوتية';
        }
    });
    
    // Form validation
    document.getElementById('healthRecordForm').addEventListener('submit', function(e) {
        const recordDate = new Date(document.getElementById('record_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (recordDate > today) {
            alert('لا يمكن تسجيل سجل صحي لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
        
        const followUpRequired = document.getElementById('follow_up_required').checked;
        const followUpDate = document.getElementById('follow_up_date').value;
        
        if (followUpRequired && !followUpDate) {
            alert('يرجى تحديد تاريخ المتابعة');
            e.preventDefault();
            return false;
        }
        
        if (followUpDate && new Date(followUpDate) <= recordDate) {
            alert('تاريخ المتابعة يجب أن يكون بعد تاريخ السجل');
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}

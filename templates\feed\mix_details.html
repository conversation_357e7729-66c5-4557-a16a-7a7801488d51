{% extends "base.html" %}

{% block title %}تفاصيل الخلطة العلفية - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-blender me-2"></i>
                تفاصيل الخلطة العلفية
            </h2>
            <p class="text-muted mb-0">{{ mix.name }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <button class="btn btn-warning" disabled>
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </button>
                <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Mix Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>اسم الخلطة:</strong></div>
                <div class="col-sm-8">{{ mix.name }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الكمية الإجمالية:</strong></div>
                <div class="col-sm-8">{{ "%.1f"|format(mix.total_quantity_kg) }} كغ</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التكلفة الإجمالية:</strong></div>
                <div class="col-sm-8">
                    <span class="text-danger fw-bold">{{ "%.2f"|format(mix.total_cost_jod) }} د.أ</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>تكلفة الكيلو:</strong></div>
                <div class="col-sm-8">
                    <span class="text-danger fw-bold">{{ "%.3f"|format(nutritional_summary.cost_per_kg) }} د.أ/كغ</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الحالة:</strong></div>
                <div class="col-sm-8">
                    {% if mix.is_active %}
                        <span class="badge bg-success">نشطة</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشطة</span>
                    {% endif %}
                </div>
            </div>
            
            {% if mix.target_animal_type %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>نوع الحيوان المستهدف:</strong></div>
                <div class="col-sm-8">{{ mix.target_animal_type }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Nutritional Summary -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-pie me-2"></i>
                الملخص الغذائي للخلطة
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>البروتين:</strong></div>
                <div class="col-sm-6">
                    <span class="badge bg-primary">{{ "%.1f"|format(nutritional_summary.protein) }}%</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الطاقة:</strong></div>
                <div class="col-sm-6">
                    <span class="badge bg-success">{{ "%.1f"|format(nutritional_summary.energy) }} ميجا كالوري/كغ</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الألياف:</strong></div>
                <div class="col-sm-6">
                    <span class="badge bg-warning">{{ "%.1f"|format(nutritional_summary.fiber) }}%</span>
                </div>
            </div>
            
            <!-- Nutritional Quality Assessment -->
            <div class="mt-4">
                <h6>تقييم الجودة الغذائية:</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-primary" role="progressbar" 
                         style="width: {{ (nutritional_summary.protein / 25 * 100)|round }}%">
                        بروتين
                    </div>
                </div>
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ (nutritional_summary.energy / 4 * 100)|round }}%">
                        طاقة
                    </div>
                </div>
                <div class="progress">
                    <div class="progress-bar bg-warning" role="progressbar" 
                         style="width: {{ (nutritional_summary.fiber / 40 * 100)|round }}%">
                        ألياف
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mix Components -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-list me-2"></i>
                مكونات الخلطة
            </h5>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المكون</th>
                            <th>النسبة المئوية</th>
                            <th>الكمية (كغ)</th>
                            <th>البروتين (%)</th>
                            <th>الطاقة (ميجا كالوري/كغ)</th>
                            <th>الألياف (%)</th>
                            <th>التكلفة (د.أ)</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mix_component in mix.components %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if mix_component.component.category == 'concentrate' %}
                                        <i class="fas fa-seedling text-primary me-2"></i>
                                    {% elif mix_component.component.category == 'roughage' %}
                                        <i class="fas fa-leaf text-success me-2"></i>
                                    {% elif mix_component.component.category == 'supplement' %}
                                        <i class="fas fa-pills text-info me-2"></i>
                                    {% else %}
                                        <i class="fas fa-cube text-secondary me-2"></i>
                                    {% endif %}
                                    <div>
                                        <strong>{{ mix_component.component.name }}</strong>
                                        {% if mix_component.component.category %}
                                            <br><small class="text-muted">{{ mix_component.component.category }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ "%.1f"|format(mix_component.percentage) }}%</span>
                            </td>
                            <td>{{ "%.1f"|format(mix_component.quantity_kg) }} كغ</td>
                            <td>{{ "%.1f"|format(mix_component.component.protein_percentage) }}%</td>
                            <td>{{ "%.1f"|format(mix_component.component.energy_mcal_kg) }}</td>
                            <td>{{ "%.1f"|format(mix_component.component.fiber_percentage) }}%</td>
                            <td>{{ "%.2f"|format(mix_component.quantity_kg * mix_component.component.cost_per_kg_jod) }} د.أ</td>
                            <td>
                                <a href="{{ url_for('feed_component_details', component_id=mix_component.component.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="عرض تفاصيل المكون">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-active">
                            <th>الإجمالي</th>
                            <th>100%</th>
                            <th>{{ "%.1f"|format(mix.total_quantity_kg) }} كغ</th>
                            <th>{{ "%.1f"|format(nutritional_summary.protein) }}%</th>
                            <th>{{ "%.1f"|format(nutritional_summary.energy) }}</th>
                            <th>{{ "%.1f"|format(nutritional_summary.fiber) }}%</th>
                            <th>{{ "%.2f"|format(mix.total_cost_jod) }} د.أ</th>
                            <th>-</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Description and Notes -->
{% if mix.description or mix.notes %}
<div class="row mt-4">
    {% if mix.description %}
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-file-text me-2"></i>
                الوصف
            </h5>
            <p class="mb-0">{{ mix.description }}</p>
        </div>
    </div>
    {% endif %}
    
    {% if mix.notes %}
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ mix.notes }}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                <button class="btn btn-warning me-2" disabled>
                    <i class="fas fa-edit me-1"></i>
                    تعديل الخلطة
                </button>
                <button class="btn btn-success me-2" disabled>
                    <i class="fas fa-copy me-1"></i>
                    نسخ الخلطة
                </button>
                <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء خلطة جديدة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

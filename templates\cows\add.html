{% extends "base.html" %}

{% block title %}إضافة بقرة جديدة - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus me-2"></i>
                إضافة بقرة جديدة
            </h2>
            <p class="text-muted mb-0">إدخال بيانات بقرة جديدة في المزرعة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('cows_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Add Cow Form -->
<div class="content-card">
    <form method="POST">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="tag_number" class="form-label">رقم الأذن <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="tag_number" name="tag_number" required>
                    <div class="form-text">رقم فريد لتمييز البقرة</div>
                </div>
                
                <div class="mb-3">
                    <label for="name" class="form-label">الاسم</label>
                    <input type="text" class="form-control" id="name" name="name">
                </div>
                
                <div class="mb-3">
                    <label for="breed" class="form-label">السلالة</label>
                    <select class="form-select" id="breed" name="breed">
                        <option value="">اختر السلالة</option>
                        <option value="Holstein">هولشتاين</option>
                        <option value="Jersey">جيرسي</option>
                        <option value="Brown Swiss">براون سويس</option>
                        <option value="Guernsey">جيرنسي</option>
                        <option value="Ayrshire">أيرشاير</option>
                        <option value="Mixed">مختلطة</option>
                        <option value="Other">أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="active">نشطة</option>
                        <option value="pregnant">حامل</option>
                        <option value="dry">جافة</option>
                        <option value="sick">مريضة</option>
                    </select>
                </div>
            </div>
            
            <!-- Dates and Purchase Info -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-calendar me-2"></i>
                    التواريخ ومعلومات الشراء
                </h5>
                
                <div class="mb-3">
                    <label for="birth_date" class="form-label">تاريخ الولادة</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_date" class="form-label">تاريخ الشراء</label>
                    <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_price_jod" class="form-label">سعر الشراء (دينار أردني)</label>
                    <input type="number" step="0.01" class="form-control" id="purchase_price_jod" name="purchase_price_jod">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_weight_kg" class="form-label">وزن الشراء (كغ)</label>
                    <input type="number" step="0.1" class="form-control" id="purchase_weight_kg" name="purchase_weight_kg">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Physical Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-weight me-2"></i>
                    المعلومات الجسدية
                </h5>
                
                <div class="mb-3">
                    <label for="current_weight_kg" class="form-label">الوزن الحالي (كغ)</label>
                    <input type="number" step="0.1" class="form-control" id="current_weight_kg" name="current_weight_kg">
                </div>
            </div>
            
            <!-- Genetic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-dna me-2"></i>
                    المعلومات الوراثية
                </h5>
                
                <div class="mb-3">
                    <label for="sire_name" class="form-label">اسم الأب</label>
                    <input type="text" class="form-control" id="sire_name" name="sire_name">
                </div>
                
                <div class="mb-3">
                    <label for="dam_name" class="form-label">اسم الأم</label>
                    <input type="text" class="form-control" id="dam_name" name="dam_name">
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div class="mb-4">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="3" 
                      placeholder="أي ملاحظات إضافية حول البقرة"></textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('cows_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ البقرة
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default for purchase date if birth date is not set
document.addEventListener('DOMContentLoaded', function() {
    const purchaseDateInput = document.getElementById('purchase_date');
    const birthDateInput = document.getElementById('birth_date');
    
    // Set default purchase date to today
    const today = new Date().toISOString().split('T')[0];
    purchaseDateInput.value = today;
    
    // Validate that birth date is before purchase date
    function validateDates() {
        const birthDate = new Date(birthDateInput.value);
        const purchaseDate = new Date(purchaseDateInput.value);
        
        if (birthDate && purchaseDate && birthDate > purchaseDate) {
            alert('تاريخ الولادة يجب أن يكون قبل تاريخ الشراء');
            birthDateInput.focus();
            return false;
        }
        return true;
    }
    
    birthDateInput.addEventListener('change', validateDates);
    purchaseDateInput.addEventListener('change', validateDates);
    
    // Form validation before submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!validateDates()) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}تعديل المصروف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل المصروف
            </h2>
            <p class="text-muted mb-0">تحديث بيانات {{ expense.description[:50] }}...</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Edit Expense Form -->
<div class="content-card">
    <form method="POST" id="expenseForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="feed" {% if expense.category == 'feed' %}selected{% endif %}>علف</option>
                        <option value="veterinary" {% if expense.category == 'veterinary' %}selected{% endif %}>بيطري</option>
                        <option value="labor" {% if expense.category == 'labor' %}selected{% endif %}>عمالة</option>
                        <option value="utilities" {% if expense.category == 'utilities' %}selected{% endif %}>مرافق (كهرباء، ماء، غاز)</option>
                        <option value="maintenance" {% if expense.category == 'maintenance' %}selected{% endif %}>صيانة</option>
                        <option value="equipment" {% if expense.category == 'equipment' %}selected{% endif %}>معدات</option>
                        <option value="transport" {% if expense.category == 'transport' %}selected{% endif %}>نقل</option>
                        <option value="insurance" {% if expense.category == 'insurance' %}selected{% endif %}>تأمين</option>
                        <option value="other" {% if expense.category == 'other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="subcategory" class="form-label">الفئة الفرعية</label>
                    <input type="text" class="form-control" id="subcategory" name="subcategory" 
                           value="{{ expense.subcategory or '' }}"
                           placeholder="مثل: شعير، أدوية، راتب">
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="2" 
                              placeholder="وصف تفصيلي للمصروف" required>{{ expense.description }}</textarea>
                </div>
                
                <div class="mb-3">
                    <label for="expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="expense_date" name="expense_date" 
                           value="{{ expense.expense_date.strftime('%Y-%m-%d') }}" required>
                </div>
            </div>
            
            <!-- Financial Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    المعلومات المالية
                </h5>
                
                <div class="mb-3">
                    <label for="amount_jod" class="form-label">المبلغ الإجمالي (دينار أردني) <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" class="form-control" id="amount_jod" name="amount_jod" 
                           value="{{ expense.amount_jod }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="quantity" class="form-label">الكمية</label>
                    <input type="number" step="0.1" class="form-control" id="quantity" name="quantity" 
                           value="{{ expense.quantity or '' }}"
                           placeholder="الكمية المشتراة">
                </div>
                
                <div class="mb-3">
                    <label for="unit" class="form-label">الوحدة</label>
                    <select class="form-select" id="unit" name="unit">
                        <option value="">اختر الوحدة</option>
                        <option value="kg" {% if expense.unit == 'kg' %}selected{% endif %}>كيلوغرام</option>
                        <option value="liter" {% if expense.unit == 'liter' %}selected{% endif %}>لتر</option>
                        <option value="ton" {% if expense.unit == 'ton' %}selected{% endif %}>طن</option>
                        <option value="piece" {% if expense.unit == 'piece' %}selected{% endif %}>قطعة</option>
                        <option value="hour" {% if expense.unit == 'hour' %}selected{% endif %}>ساعة</option>
                        <option value="day" {% if expense.unit == 'day' %}selected{% endif %}>يوم</option>
                        <option value="month" {% if expense.unit == 'month' %}selected{% endif %}>شهر</option>
                        <option value="service" {% if expense.unit == 'service' %}selected{% endif %}>خدمة</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="unit_price_jod" class="form-label">سعر الوحدة (دينار أردني)</label>
                    <input type="number" step="0.001" class="form-control" id="unit_price_jod" name="unit_price_jod" 
                           value="{{ expense.unit_price_jod or '' }}"
                           placeholder="سعر الوحدة الواحدة" readonly>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Supplier Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-truck me-2"></i>
                    معلومات المورد
                </h5>
                
                <div class="mb-3">
                    <label for="supplier_name" class="form-label">اسم المورد</label>
                    <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                           value="{{ expense.supplier_name or '' }}"
                           placeholder="اسم المورد أو الشركة">
                </div>
                
                <div class="mb-3">
                    <label for="invoice_number" class="form-label">رقم الفاتورة</label>
                    <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                           value="{{ expense.invoice_number or '' }}"
                           placeholder="رقم الفاتورة أو الإيصال">
                </div>
            </div>
            
            <!-- Payment Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    معلومات الدفع
                </h5>
                
                <div class="mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="cash" {% if expense.payment_method == 'cash' %}selected{% endif %}>نقدي</option>
                        <option value="bank_transfer" {% if expense.payment_method == 'bank_transfer' %}selected{% endif %}>تحويل بنكي</option>
                        <option value="check" {% if expense.payment_method == 'check' %}selected{% endif %}>شيك</option>
                        <option value="credit_card" {% if expense.payment_method == 'credit_card' %}selected{% endif %}>بطاقة ائتمان</option>
                        <option value="debit_card" {% if expense.payment_method == 'debit_card' %}selected{% endif %}>بطاقة خصم</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية حول المصروف">{{ expense.notes or '' }}</textarea>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate unit price automatically
    function calculateUnitPrice() {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const unitPriceInput = document.getElementById('unit_price_jod');
        
        if (amount > 0 && quantity > 0) {
            const unitPrice = amount / quantity;
            unitPriceInput.value = unitPrice.toFixed(3);
        } else {
            unitPriceInput.value = '';
        }
    }
    
    // Add event listeners for automatic calculation
    document.getElementById('amount_jod').addEventListener('input', calculateUnitPrice);
    document.getElementById('quantity').addEventListener('input', calculateUnitPrice);
    
    // Form validation
    document.getElementById('expenseForm').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const expenseDate = new Date(document.getElementById('expense_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (amount <= 0) {
            alert('المبلغ يجب أن يكون أكبر من صفر');
            e.preventDefault();
            return false;
        }
        
        if (expenseDate > today) {
            alert('لا يمكن تسجيل مصروف لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
    });
    
    // Calculate unit price on page load
    calculateUnitPrice();
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}تفاصيل الإيراد - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus-circle me-2"></i>
                تفاصيل الإيراد
            </h2>
            <p class="text-muted mb-0">{{ revenue.description[:50] }}...</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('edit_revenue', revenue_id=revenue.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="{{ url_for('revenues_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>مصدر الإيراد:</strong></div>
                <div class="col-sm-8">
                    {% if revenue.source == 'milk_sale' %}
                        <span class="badge bg-primary">بيع حليب</span>
                    {% elif revenue.source == 'cow_sale' %}
                        <span class="badge bg-success">بيع أبقار</span>
                    {% elif revenue.source == 'calf_sale' %}
                        <span class="badge bg-info">بيع عجول</span>
                    {% elif revenue.source == 'manure_sale' %}
                        <span class="badge bg-warning">بيع سماد</span>
                    {% elif revenue.source == 'breeding_service' %}
                        <span class="badge bg-danger">خدمات التلقيح</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ revenue.source }}</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>تاريخ البيع:</strong></div>
                <div class="col-sm-8">{{ revenue.sale_date.strftime('%Y-%m-%d') }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الوصف:</strong></div>
                <div class="col-sm-8">{{ revenue.description }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>حالة الدفع:</strong></div>
                <div class="col-sm-8">
                    {% if revenue.payment_status == 'paid' %}
                        <span class="badge bg-success">مدفوع</span>
                    {% elif revenue.payment_status == 'pending' %}
                        <span class="badge bg-warning">معلق</span>
                    {% elif revenue.payment_status == 'overdue' %}
                        <span class="badge bg-danger">متأخر</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ revenue.payment_status }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Financial Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-money-bill-wave me-2"></i>
                المعلومات المالية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>المبلغ الإجمالي:</strong></div>
                <div class="col-sm-8">
                    <span class="text-success fw-bold fs-5">{{ "%.2f"|format(revenue.amount_jod) }} د.أ</span>
                </div>
            </div>
            
            {% if revenue.quantity_liters %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>كمية الحليب:</strong></div>
                <div class="col-sm-8">{{ "%.1f"|format(revenue.quantity_liters) }} لتر</div>
            </div>
            {% endif %}
            
            {% if revenue.price_per_liter_jod %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>سعر اللتر:</strong></div>
                <div class="col-sm-8">{{ "%.3f"|format(revenue.price_per_liter_jod) }} د.أ/لتر</div>
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>طريقة الدفع:</strong></div>
                <div class="col-sm-8">
                    {% if revenue.payment_method == 'cash' %}
                        <span class="badge bg-success">نقدي</span>
                    {% elif revenue.payment_method == 'bank_transfer' %}
                        <span class="badge bg-primary">تحويل بنكي</span>
                    {% elif revenue.payment_method == 'check' %}
                        <span class="badge bg-warning">شيك</span>
                    {% elif revenue.payment_method == 'credit_card' %}
                        <span class="badge bg-info">بطاقة ائتمان</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ revenue.payment_method }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Information -->
{% if revenue.customer_name or revenue.customer_phone %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-user me-2"></i>
                معلومات العميل
            </h5>
            
            {% if revenue.customer_name %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>اسم العميل:</strong></div>
                <div class="col-sm-8">{{ revenue.customer_name }}</div>
            </div>
            {% endif %}
            
            {% if revenue.customer_phone %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>هاتف العميل:</strong></div>
                <div class="col-sm-8">{{ revenue.customer_phone }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Additional Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-clock me-2"></i>
                معلومات إضافية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>تاريخ الإنشاء:</strong></div>
                <div class="col-sm-8">{{ revenue.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
            </div>
            
            {% if revenue.updated_at and revenue.updated_at != revenue.created_at %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>آخر تحديث:</strong></div>
                <div class="col-sm-8">{{ revenue.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
            </div>
            {% endif %}
            
            {% if revenue.created_by_user %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>أنشأ بواسطة:</strong></div>
                <div class="col-sm-8">{{ revenue.created_by_user.username }}</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Notes -->
{% if revenue.notes %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ revenue.notes }}</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Revenue Analysis -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-line me-2"></i>
                تحليل الإيراد
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-calendar-day text-success fa-2x mb-2"></i>
                        <h6>إيراد يومي</h6>
                        <p class="mb-0 text-success fw-bold">{{ "%.2f"|format(revenue.amount_jod) }} د.أ</p>
                    </div>
                </div>
                
                {% if revenue.quantity_liters %}
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-tint text-primary fa-2x mb-2"></i>
                        <h6>إيراد لكل لتر</h6>
                        <p class="mb-0 text-primary fw-bold">{{ "%.3f"|format(revenue.price_per_liter_jod or 0) }} د.أ</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-percentage text-warning fa-2x mb-2"></i>
                        <h6>نسبة من المصدر</h6>
                        <p class="mb-0 text-warning fw-bold">--%</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-center p-3 border rounded">
                        <i class="fas fa-chart-line text-info fa-2x mb-2"></i>
                        <h6>الاتجاه الشهري</h6>
                        <p class="mb-0 text-info fw-bold">--%</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profitability Analysis (if milk sale) -->
{% if revenue.source == 'milk_sale' and revenue.quantity_liters %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-calculator me-2"></i>
                تحليل الربحية (تقديري)
            </h5>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                هذا التحليل تقديري ويحتاج إلى ربطه بتكاليف الإنتاج الفعلية
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6>الإيراد الإجمالي</h6>
                        <p class="mb-0 text-success fw-bold">{{ "%.2f"|format(revenue.amount_jod) }} د.أ</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6>التكلفة التقديرية</h6>
                        <p class="mb-0 text-warning fw-bold">-- د.أ</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center p-3 border rounded">
                        <h6>الربح التقديري</h6>
                        <p class="mb-0 text-info fw-bold">-- د.أ</p>
                        <small class="text-muted">سيتم حسابها لاحقاً</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('revenues_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                <a href="{{ url_for('edit_revenue', revenue_id=revenue.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل الإيراد
                </a>
                <button class="btn btn-success me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <a href="{{ url_for('add_revenue') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة إيراد جديد
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .page-header .col-auto, .row:last-child {
        display: none !important;
    }
    .content-card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

{% extends "base.html" %}

{% block title %}تفاصيل مكون العلف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-seedling me-2"></i>
                تفاصيل مكون العلف
            </h2>
            <p class="text-muted mb-0">{{ component.name }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('edit_feed_component', component_id=component.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Component Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الاسم العربي:</strong></div>
                <div class="col-sm-8">{{ component.name }}</div>
            </div>
            
            {% if component.name_english %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الاسم الإنجليزي:</strong></div>
                <div class="col-sm-8">{{ component.name_english }}</div>
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الفئة:</strong></div>
                <div class="col-sm-8">
                    {% if component.category == 'concentrate' %}
                        <span class="badge bg-primary">مركزات</span>
                    {% elif component.category == 'roughage' %}
                        <span class="badge bg-success">أعلاف خشنة</span>
                    {% elif component.category == 'supplement' %}
                        <span class="badge bg-info">مكملات غذائية</span>
                    {% elif component.category == 'mineral' %}
                        <span class="badge bg-warning">أملاح معدنية</span>
                    {% elif component.category == 'vitamin' %}
                        <span class="badge bg-danger">فيتامينات</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ component.category }}</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التكلفة لكل كيلو:</strong></div>
                <div class="col-sm-8">
                    <span class="text-danger fw-bold">{{ "%.3f"|format(component.cost_per_kg_jod) }} د.أ</span>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الحالة:</strong></div>
                <div class="col-sm-8">
                    {% if component.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Nutritional Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-chart-pie me-2"></i>
                القيم الغذائية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>البروتين:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.protein_percentage) }}%</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الطاقة:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.energy_mcal_kg) }} ميجا كالوري/كغ</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الألياف:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.fiber_percentage) }}%</div>
            </div>
            
            {% if component.fat_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الدهون:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.fat_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if component.ash_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الرماد:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.ash_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if component.moisture_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الرطوبة:</strong></div>
                <div class="col-sm-6">{{ "%.1f"|format(component.moisture_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if component.calcium_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الكالسيوم:</strong></div>
                <div class="col-sm-6">{{ "%.2f"|format(component.calcium_percentage) }}%</div>
            </div>
            {% endif %}
            
            {% if component.phosphorus_percentage %}
            <div class="row mb-3">
                <div class="col-sm-6"><strong>الفوسفور:</strong></div>
                <div class="col-sm-6">{{ "%.2f"|format(component.phosphorus_percentage) }}%</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Supplier and Storage Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-truck me-2"></i>
                معلومات المورد
            </h5>
            
            {% if component.supplier %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>المورد:</strong></div>
                <div class="col-sm-8">{{ component.supplier }}</div>
            </div>
            {% endif %}
            
            {% if component.supplier_phone %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>هاتف المورد:</strong></div>
                <div class="col-sm-8">{{ component.supplier_phone }}</div>
            </div>
            {% endif %}
            
            {% if not component.supplier and not component.supplier_phone %}
            <p class="text-muted">لا توجد معلومات مورد</p>
            {% endif %}
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-warehouse me-2"></i>
                معلومات التخزين
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>مدة الصلاحية:</strong></div>
                <div class="col-sm-8">{{ component.shelf_life_days }} يوم</div>
            </div>
            
            {% if component.storage_requirements %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>متطلبات التخزين:</strong></div>
                <div class="col-sm-8">{{ component.storage_requirements }}</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Description -->
{% if component.description %}
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-file-text me-2"></i>
                الوصف
            </h5>
            <p class="mb-0">{{ component.description }}</p>
        </div>
    </div>
</div>
{% endif %}

<!-- Mixes Using This Component -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-blender me-2"></i>
                الخلطات العلفية التي تستخدم هذا المكون
            </h5>
            
            {% if mixes_using_component %}
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>اسم الخلطة</th>
                            <th>النسبة المئوية</th>
                            <th>الكمية (كغ)</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mix in mixes_using_component %}
                        {% set mix_component = mix.components.filter_by(component_id=component.id).first() %}
                        <tr>
                            <td>
                                <a href="{{ url_for('feed_mix_details', mix_id=mix.id) }}" class="text-decoration-none">
                                    {{ mix.name }}
                                </a>
                            </td>
                            <td>{{ "%.1f"|format(mix_component.percentage) }}%</td>
                            <td>{{ "%.1f"|format(mix_component.quantity_kg) }} كغ</td>
                            <td>
                                <a href="{{ url_for('feed_mix_details', mix_id=mix.id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-3">
                <i class="fas fa-info-circle text-muted fa-2x mb-2"></i>
                <p class="text-muted mb-0">هذا المكون غير مستخدم في أي خلطة علفية حالياً</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                <a href="{{ url_for('edit_feed_component', component_id=component.id) }}" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل المكون
                </a>
                <a href="{{ url_for('add_feed_mix') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    إنشاء خلطة جديدة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة مزرعة الألبان{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.8em;
            padding: 0.5em 0.8em;
        }
        .stats-mini {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .stats-mini .number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }
        .stats-mini .label {
            font-size: 0.9rem;
            color: #666;
        }
        {% block extra_css %}{% endblock %}
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-cow me-2"></i>
                نظام إدارة مزرعة الألبان
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and 'cows' in request.endpoint %}active{% endif %}" href="#" id="cowsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cow me-1"></i>
                            إدارة الأبقار
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('cows_list') }}">
                                <i class="fas fa-list me-1"></i>
                                قائمة الأبقار
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_cow') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة بقرة جديدة
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and 'milk' in request.endpoint %}active{% endif %}" href="#" id="milkDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-glass-whiskey me-1"></i>
                            إنتاج الحليب
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('milk_production_list') }}">
                                <i class="fas fa-list me-1"></i>
                                سجلات الإنتاج
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_milk_production') }}">
                                <i class="fas fa-plus me-1"></i>
                                تسجيل إنتاج جديد
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and 'feed' in request.endpoint %}active{% endif %}" href="#" id="feedDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-seedling me-1"></i>
                            العلف والتغذية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('feed_components_list') }}">
                                <i class="fas fa-list me-1"></i>
                                مكونات العلف
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_feed_component') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مكون جديد
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('feed_mixes_list') }}">
                                <i class="fas fa-blender me-1"></i>
                                الخلطات العلفية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_feed_mix') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة خلطة جديدة
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and 'health' in request.endpoint %}active{% endif %}" href="#" id="healthDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-heartbeat me-1"></i>
                            السجلات الصحية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('health_records_list') }}">
                                <i class="fas fa-list me-1"></i>
                                السجلات الصحية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_health_record') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة سجل صحي
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and ('expense' in request.endpoint or 'revenue' in request.endpoint) %}active{% endif %}" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            الإدارة المالية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('expenses_list') }}">
                                <i class="fas fa-minus-circle me-1"></i>
                                المصروفات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_expense') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة مصروف
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('revenues_list') }}">
                                <i class="fas fa-plus-circle me-1"></i>
                                الإيرادات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_revenue') }}">
                                <i class="fas fa-plus me-1"></i>
                                إضافة إيراد
                            </a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name or current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

{% extends "base.html" %}

{% block title %}الخلطات العلفية - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-blender me-2"></i>
                الخلطات العلفية
            </h2>
            <p class="text-muted mb-0">إدارة الخلطات العلفية المخصصة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة خلطة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ total_mixes }}</div>
            <div class="label">إجمالي الخلطات</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ lactating_mixes }}</div>
            <div class="label">خلطات الحلوب</div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ dry_mixes }}</div>
            <div class="label">خلطات الجافة</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">البحث</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search }}" placeholder="اسم الخلطة أو الوصف">
        </div>
        <div class="col-md-3">
            <label for="target_group" class="form-label">المجموعة المستهدفة</label>
            <select class="form-select" id="target_group" name="target_group">
                <option value="">جميع المجموعات</option>
                <option value="lactating" {% if target_filter == 'lactating' %}selected{% endif %}>أبقار حلوب</option>
                <option value="dry" {% if target_filter == 'dry' %}selected{% endif %}>أبقار جافة</option>
                <option value="pregnant" {% if target_filter == 'pregnant' %}selected{% endif %}>أبقار حامل</option>
                <option value="young" {% if target_filter == 'young' %}selected{% endif %}>عجول</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Mixes Table -->
<div class="content-card">
    {% if mixes.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم الخلطة</th>
                        <th>المجموعة المستهدفة</th>
                        <th>البروتين (%)</th>
                        <th>الطاقة (Mcal/kg)</th>
                        <th>الألياف (%)</th>
                        <th>التكلفة (د.أ/كغ)</th>
                        <th>الكمية اليومية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for mix in mixes.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ mix.name }}</strong>
                                {% if mix.description %}
                                    <br><small class="text-muted">{{ mix.description[:50] }}{% if mix.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if mix.target_group == 'lactating' %}
                                <span class="badge bg-primary">أبقار حلوب</span>
                            {% elif mix.target_group == 'dry' %}
                                <span class="badge bg-warning">أبقار جافة</span>
                            {% elif mix.target_group == 'pregnant' %}
                                <span class="badge bg-info">أبقار حامل</span>
                            {% elif mix.target_group == 'young' %}
                                <span class="badge bg-success">عجول</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ mix.target_group }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.total_protein_percentage %}
                                {{ "%.1f"|format(mix.total_protein_percentage) }}%
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.total_energy_mcal_kg %}
                                {{ "%.2f"|format(mix.total_energy_mcal_kg) }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.total_fiber_percentage %}
                                {{ "%.1f"|format(mix.total_fiber_percentage) }}%
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.total_cost_per_kg_jod %}
                                {{ "%.3f"|format(mix.total_cost_per_kg_jod) }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.recommended_daily_amount_kg %}
                                {{ mix.recommended_daily_amount_kg }} كغ
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('feed_mix_details', mix_id=mix.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-warning" title="تعديل" disabled>
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" title="نسخ الخلطة" disabled>
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ mix.name }}', '{{ url_for('delete_feed_mix', mix_id=mix.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if mixes.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if mixes.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('feed_mixes_list', page=mixes.prev_num, search=search, target_group=target_filter) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in mixes.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != mixes.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feed_mixes_list', page=page_num, search=search, target_group=target_filter) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if mixes.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('feed_mixes_list', page=mixes.next_num, search=search, target_group=target_filter) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-blender fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد خلطات علفية</h5>
            <p class="text-muted">ابدأ بإنشاء أول خلطة علفية</p>
            <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة خلطة جديدة
            </a>
        </div>
    {% endif %}
</div>


{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف الخلطة العلفية "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}مكونات العلف - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-seedling me-2"></i>
                مكونات العلف
            </h2>
            <p class="text-muted mb-0">إدارة مكونات العلف والقيم الغذائية</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_feed_component') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مكون جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ total_components }}</div>
            <div class="label">إجمالي المكونات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ concentrate_count }}</div>
            <div class="label">مركزات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ roughage_count }}</div>
            <div class="label">أعلاف خشنة</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ supplement_count }}</div>
            <div class="label">مكملات</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">البحث</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search }}" placeholder="اسم المكون أو المورد">
        </div>
        <div class="col-md-3">
            <label for="category" class="form-label">الفئة</label>
            <select class="form-select" id="category" name="category">
                <option value="">جميع الفئات</option>
                <option value="concentrate" {% if category_filter == 'concentrate' %}selected{% endif %}>مركزات</option>
                <option value="roughage" {% if category_filter == 'roughage' %}selected{% endif %}>أعلاف خشنة</option>
                <option value="supplement" {% if category_filter == 'supplement' %}selected{% endif %}>مكملات</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Components Table -->
<div class="content-card">
    {% if components.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>اسم المكون</th>
                        <th>الفئة</th>
                        <th>البروتين (%)</th>
                        <th>الطاقة (Mcal/kg)</th>
                        <th>الألياف (%)</th>
                        <th>التكلفة (د.أ/كغ)</th>
                        <th>المورد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for component in components.items %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ component.name }}</strong>
                                {% if component.name_english %}
                                    <br><small class="text-muted">{{ component.name_english }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if component.category == 'concentrate' %}
                                <span class="badge bg-primary">مركزات</span>
                            {% elif component.category == 'roughage' %}
                                <span class="badge bg-success">أعلاف خشنة</span>
                            {% elif component.category == 'supplement' %}
                                <span class="badge bg-warning">مكملات</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ component.category }}</span>
                            {% endif %}
                        </td>
                        <td>{{ "%.1f"|format(component.protein_percentage) }}%</td>
                        <td>{{ "%.2f"|format(component.energy_mcal_kg) }}</td>
                        <td>{{ "%.1f"|format(component.fiber_percentage) }}%</td>
                        <td>{{ "%.3f"|format(component.cost_per_kg_jod) }}</td>
                        <td>
                            {% if component.supplier %}
                                {{ component.supplier }}
                                {% if component.supplier_phone %}
                                    <br><small class="text-muted">{{ component.supplier_phone }}</small>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('feed_component_details', component_id=component.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_feed_component', component_id=component.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ component.name }}', '{{ url_for('delete_feed_component', component_id=component.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if components.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if components.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('feed_components_list', page=components.prev_num, search=search, category=category_filter) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in components.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != components.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('feed_components_list', page=page_num, search=search, category=category_filter) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if components.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('feed_components_list', page=components.next_num, search=search, category=category_filter) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مكونات علف</h5>
            <p class="text-muted">ابدأ بإضافة أول مكون علف</p>
            <a href="{{ url_for('add_feed_component') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مكون جديد
            </a>
        </div>
    {% endif %}
</div>


{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف مكون العلف "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

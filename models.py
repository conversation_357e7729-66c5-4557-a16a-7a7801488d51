# models.py - نماذج قاعدة البيانات المتكاملة
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين مع نظام صلاحيات متقدم"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, manager, user
    full_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class FeedComponent(db.Model):
    """نموذج مكونات العلف مع البيانات الغذائية الشاملة"""
    __tablename__ = 'feed_components'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_english = db.Column(db.String(100))
    category = db.Column(db.String(50))  # concentrate, roughage, supplement
    
    # القيم الغذائية الأساسية
    protein_percentage = db.Column(db.Float, nullable=False)  # نسبة البروتين الخام
    energy_mcal_kg = db.Column(db.Float, nullable=False)      # الطاقة الأيضية
    fiber_percentage = db.Column(db.Float, nullable=False)    # نسبة الألياف
    fat_percentage = db.Column(db.Float, default=0)          # نسبة الدهون
    ash_percentage = db.Column(db.Float, default=0)          # نسبة الرماد
    moisture_percentage = db.Column(db.Float, default=0)     # نسبة الرطوبة
    
    # المعادن والفيتامينات
    calcium_percentage = db.Column(db.Float, default=0)      # نسبة الكالسيوم
    phosphorus_percentage = db.Column(db.Float, default=0)   # نسبة الفسفور
    
    # التكلفة والتوافر
    cost_per_kg_jod = db.Column(db.Float, nullable=False)    # التكلفة بالدينار الأردني
    supplier = db.Column(db.String(100))                     # اسم المورد
    supplier_phone = db.Column(db.String(20))
    
    # بيانات إضافية
    description = db.Column(db.Text)
    storage_requirements = db.Column(db.Text)                # متطلبات التخزين
    shelf_life_days = db.Column(db.Integer, default=30)      # مدة الصلاحية بالأيام
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class FeedMix(db.Model):
    """نموذج الخلطات العلفية مع حسابات غذائية متقدمة"""
    __tablename__ = 'feed_mixes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    target_group = db.Column(db.String(50))  # lactating, dry, heifer, calf
    
    # القيم الغذائية المحسوبة للخلطة
    total_protein_percentage = db.Column(db.Float, default=0)
    total_energy_mcal_kg = db.Column(db.Float, default=0)
    total_fiber_percentage = db.Column(db.Float, default=0)
    total_fat_percentage = db.Column(db.Float, default=0)
    
    # التكلفة والكمية
    total_cost_per_kg_jod = db.Column(db.Float, default=0)
    total_weight_kg = db.Column(db.Float, default=0)
    
    # بيانات إضافية
    feeding_instructions = db.Column(db.Text)                # تعليمات التغذية
    recommended_daily_amount_kg = db.Column(db.Float)        # الكمية اليومية الموصى بها
    
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    components = db.relationship('FeedMixComponent', backref='feed_mix', lazy=True, cascade='all, delete-orphan')
    creator = db.relationship('User', backref='created_mixes')

class FeedMixComponent(db.Model):
    """نموذج مكونات الخلطة العلفية"""
    __tablename__ = 'feed_mix_components'
    
    id = db.Column(db.Integer, primary_key=True)
    feed_mix_id = db.Column(db.Integer, db.ForeignKey('feed_mixes.id'), nullable=False)
    component_id = db.Column(db.Integer, db.ForeignKey('feed_components.id'), nullable=False)
    quantity_kg = db.Column(db.Float, nullable=False)
    percentage_in_mix = db.Column(db.Float, nullable=False)
    
    # العلاقات
    component = db.relationship('FeedComponent', backref='mix_usages')

class Cow(db.Model):
    """نموذج الأبقار مع بيانات شاملة"""
    __tablename__ = 'cows'
    
    id = db.Column(db.Integer, primary_key=True)
    tag_number = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(50))
    breed = db.Column(db.String(50))                         # Holstein, Jersey, etc.
    
    # بيانات الولادة والشراء
    birth_date = db.Column(db.Date)
    purchase_date = db.Column(db.Date)
    purchase_price_jod = db.Column(db.Float)
    purchase_weight_kg = db.Column(db.Float)
    
    # الحالة الحالية
    status = db.Column(db.String(20), default='active')     # active, pregnant, dry, sick, sold, dead
    current_weight_kg = db.Column(db.Float)
    body_condition_score = db.Column(db.Float)               # 1-5 scale
    
    # بيانات التناسل
    last_breeding_date = db.Column(db.Date)
    expected_calving_date = db.Column(db.Date)
    total_calvings = db.Column(db.Integer, default=0)
    
    # بيانات إضافية
    sire_name = db.Column(db.String(50))                     # اسم الأب
    dam_name = db.Column(db.String(50))                      # اسم الأم
    notes = db.Column(db.Text)
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    milk_productions = db.relationship('MilkProduction', backref='cow', lazy=True)
    health_records = db.relationship('HealthRecord', backref='cow', lazy=True)

class MilkProduction(db.Model):
    """نموذج إنتاج الحليب مع تفاصيل شاملة"""
    __tablename__ = 'milk_productions'
    
    id = db.Column(db.Integer, primary_key=True)
    cow_id = db.Column(db.Integer, db.ForeignKey('cows.id'), nullable=False)
    production_date = db.Column(db.Date, nullable=False)
    
    # كميات الحليب
    morning_quantity_liters = db.Column(db.Float, default=0)
    evening_quantity_liters = db.Column(db.Float, default=0)
    total_quantity_liters = db.Column(db.Float, default=0)
    
    # جودة الحليب
    fat_percentage = db.Column(db.Float)                     # نسبة الدهون
    protein_percentage = db.Column(db.Float)                 # نسبة البروتين
    somatic_cell_count = db.Column(db.Integer)               # عدد الخلايا الجسدية
    quality_grade = db.Column(db.String(10), default='A')   # A, B, C
    
    # بيانات إضافية
    milker_name = db.Column(db.String(50))                   # اسم الحلاب
    temperature_celsius = db.Column(db.Float)                # درجة حرارة الحليب
    notes = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # فهارس للبحث السريع
    __table_args__ = (
        db.Index('idx_cow_date', 'cow_id', 'production_date'),
        db.Index('idx_production_date', 'production_date'),
    )

class HealthRecord(db.Model):
    """نموذج السجلات الصحية للأبقار"""
    __tablename__ = 'health_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cow_id = db.Column(db.Integer, db.ForeignKey('cows.id'), nullable=False)
    record_date = db.Column(db.Date, nullable=False)
    record_type = db.Column(db.String(20), nullable=False)   # vaccination, treatment, checkup, disease
    
    # تفاصيل السجل
    condition_diagnosis = db.Column(db.String(100))
    treatment_given = db.Column(db.Text)
    medication_used = db.Column(db.String(100))
    dosage = db.Column(db.String(50))
    veterinarian_name = db.Column(db.String(50))
    
    # التكلفة
    treatment_cost_jod = db.Column(db.Float, default=0)
    
    # المتابعة
    follow_up_required = db.Column(db.Boolean, default=False)
    follow_up_date = db.Column(db.Date)
    recovery_status = db.Column(db.String(20))               # recovered, ongoing, chronic
    
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Expense(db.Model):
    """نموذج المصروفات مع تصنيف شامل"""
    __tablename__ = 'expenses'
    
    id = db.Column(db.Integer, primary_key=True)
    category = db.Column(db.String(50), nullable=False)      # feed, veterinary, labor, utilities, maintenance
    subcategory = db.Column(db.String(50))
    description = db.Column(db.String(200), nullable=False)
    
    amount_jod = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Float)                           # الكمية إذا كانت قابلة للقياس
    unit_price_jod = db.Column(db.Float)                     # سعر الوحدة
    unit = db.Column(db.String(20))                          # kg, liter, hour, etc.
    
    expense_date = db.Column(db.Date, nullable=False)
    supplier_name = db.Column(db.String(100))
    invoice_number = db.Column(db.String(50))
    payment_method = db.Column(db.String(20))                # cash, bank_transfer, check
    
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Revenue(db.Model):
    """نموذج الإيرادات مع تفاصيل شاملة"""
    __tablename__ = 'revenues'
    
    id = db.Column(db.Integer, primary_key=True)
    source = db.Column(db.String(50), nullable=False)        # milk_sale, cow_sale, calf_sale, other
    description = db.Column(db.String(200), nullable=False)
    
    amount_jod = db.Column(db.Float, nullable=False)
    quantity_liters = db.Column(db.Float)                    # كمية الحليب باللتر
    price_per_liter_jod = db.Column(db.Float)               # سعر اللتر بالدينار
    
    sale_date = db.Column(db.Date, nullable=False)
    customer_name = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    payment_method = db.Column(db.String(20))                # cash, bank_transfer, check
    payment_status = db.Column(db.String(20), default='paid') # paid, pending, partial
    
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Inventory(db.Model):
    """نموذج إدارة المخزون"""
    __tablename__ = 'inventory'
    
    id = db.Column(db.Integer, primary_key=True)
    item_type = db.Column(db.String(20), nullable=False)     # feed_component, medicine, equipment
    item_id = db.Column(db.Integer)                          # ID of the specific item
    item_name = db.Column(db.String(100), nullable=False)
    
    current_stock_kg = db.Column(db.Float, default=0)
    minimum_stock_kg = db.Column(db.Float, default=0)
    maximum_stock_kg = db.Column(db.Float)
    
    last_purchase_date = db.Column(db.Date)
    last_purchase_price_jod = db.Column(db.Float)
    expiry_date = db.Column(db.Date)
    
    location = db.Column(db.String(50))                      # موقع التخزين
    notes = db.Column(db.Text)
    
    is_active = db.Column(db.Boolean, default=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

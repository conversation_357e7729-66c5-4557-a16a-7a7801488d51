{% extends "base.html" %}

{% block title %}إضافة مكون علف جديد - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus me-2"></i>
                إضافة مكون علف جديد
            </h2>
            <p class="text-muted mb-0">إدخال بيانات مكون علف جديد</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Add Component Form -->
<div class="content-card">
    <form method="POST" id="componentForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="name" class="form-label">اسم المكون <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                
                <div class="mb-3">
                    <label for="name_english" class="form-label">الاسم الإنجليزي</label>
                    <input type="text" class="form-control" id="name_english" name="name_english">
                </div>
                
                <div class="mb-3">
                    <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="concentrate">مركزات</option>
                        <option value="roughage">أعلاف خشنة</option>
                        <option value="supplement">مكملات</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="cost_per_kg_jod" class="form-label">التكلفة (دينار أردني/كغ) <span class="text-danger">*</span></label>
                    <input type="number" step="0.001" class="form-control" id="cost_per_kg_jod" name="cost_per_kg_jod" required>
                </div>
            </div>
            
            <!-- Nutritional Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    القيم الغذائية
                </h5>
                
                <div class="mb-3">
                    <label for="protein_percentage" class="form-label">نسبة البروتين (%) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" class="form-control" id="protein_percentage" name="protein_percentage" required>
                </div>
                
                <div class="mb-3">
                    <label for="energy_mcal_kg" class="form-label">الطاقة (Mcal/kg) <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" class="form-control" id="energy_mcal_kg" name="energy_mcal_kg" required>
                </div>
                
                <div class="mb-3">
                    <label for="fiber_percentage" class="form-label">نسبة الألياف (%) <span class="text-danger">*</span></label>
                    <input type="number" step="0.1" class="form-control" id="fiber_percentage" name="fiber_percentage" required>
                </div>
                
                <div class="mb-3">
                    <label for="fat_percentage" class="form-label">نسبة الدهون (%)</label>
                    <input type="number" step="0.1" class="form-control" id="fat_percentage" name="fat_percentage">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Additional Nutritional Values -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-flask me-2"></i>
                    قيم غذائية إضافية
                </h5>
                
                <div class="mb-3">
                    <label for="ash_percentage" class="form-label">نسبة الرماد (%)</label>
                    <input type="number" step="0.1" class="form-control" id="ash_percentage" name="ash_percentage">
                </div>
                
                <div class="mb-3">
                    <label for="moisture_percentage" class="form-label">نسبة الرطوبة (%)</label>
                    <input type="number" step="0.1" class="form-control" id="moisture_percentage" name="moisture_percentage">
                </div>
                
                <div class="mb-3">
                    <label for="calcium_percentage" class="form-label">نسبة الكالسيوم (%)</label>
                    <input type="number" step="0.01" class="form-control" id="calcium_percentage" name="calcium_percentage">
                </div>
                
                <div class="mb-3">
                    <label for="phosphorus_percentage" class="form-label">نسبة الفوسفور (%)</label>
                    <input type="number" step="0.01" class="form-control" id="phosphorus_percentage" name="phosphorus_percentage">
                </div>
            </div>
            
            <!-- Supplier Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-truck me-2"></i>
                    معلومات المورد والتخزين
                </h5>
                
                <div class="mb-3">
                    <label for="supplier" class="form-label">اسم المورد</label>
                    <input type="text" class="form-control" id="supplier" name="supplier">
                </div>
                
                <div class="mb-3">
                    <label for="supplier_phone" class="form-label">هاتف المورد</label>
                    <input type="text" class="form-control" id="supplier_phone" name="supplier_phone">
                </div>
                
                <div class="mb-3">
                    <label for="shelf_life_days" class="form-label">مدة الصلاحية (أيام)</label>
                    <input type="number" class="form-control" id="shelf_life_days" name="shelf_life_days" value="30">
                </div>
                
                <div class="mb-3">
                    <label for="storage_requirements" class="form-label">متطلبات التخزين</label>
                    <textarea class="form-control" id="storage_requirements" name="storage_requirements" rows="2" 
                              placeholder="مثل: يحفظ في مكان جاف وبارد"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Description -->
        <div class="mb-4">
            <label for="description" class="form-label">الوصف</label>
            <textarea class="form-control" id="description" name="description" rows="3" 
                      placeholder="وصف المكون وفوائده الغذائية"></textarea>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ المكون
            </button>
        </div>
    </form>
</div>

<!-- Nutritional Guidelines Card -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-lightbulb me-2"></i>
        إرشادات القيم الغذائية
    </h5>
    <div class="row">
        <div class="col-md-4">
            <h6>المركزات:</h6>
            <ul class="list-unstyled text-muted small">
                <li>• البروتين: 12-25%</li>
                <li>• الطاقة: 2.5-3.5 Mcal/kg</li>
                <li>• الألياف: 3-15%</li>
            </ul>
        </div>
        <div class="col-md-4">
            <h6>الأعلاف الخشنة:</h6>
            <ul class="list-unstyled text-muted small">
                <li>• البروتين: 5-20%</li>
                <li>• الطاقة: 1.5-2.5 Mcal/kg</li>
                <li>• الألياف: 20-45%</li>
            </ul>
        </div>
        <div class="col-md-4">
            <h6>المكملات:</h6>
            <ul class="list-unstyled text-muted small">
                <li>• البروتين: 20-50%</li>
                <li>• الطاقة: 2.0-4.0 Mcal/kg</li>
                <li>• الألياف: 0-10%</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.getElementById('componentForm').addEventListener('submit', function(e) {
        const protein = parseFloat(document.getElementById('protein_percentage').value) || 0;
        const energy = parseFloat(document.getElementById('energy_mcal_kg').value) || 0;
        const fiber = parseFloat(document.getElementById('fiber_percentage').value) || 0;
        const cost = parseFloat(document.getElementById('cost_per_kg_jod').value) || 0;
        
        if (protein < 0 || protein > 100) {
            alert('نسبة البروتين يجب أن تكون بين 0 و 100%');
            e.preventDefault();
            return false;
        }
        
        if (energy < 0 || energy > 5) {
            alert('الطاقة يجب أن تكون بين 0 و 5 Mcal/kg');
            e.preventDefault();
            return false;
        }
        
        if (fiber < 0 || fiber > 100) {
            alert('نسبة الألياف يجب أن تكون بين 0 و 100%');
            e.preventDefault();
            return false;
        }
        
        if (cost <= 0) {
            alert('التكلفة يجب أن تكون أكبر من صفر');
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-suggest based on category
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const proteinInput = document.getElementById('protein_percentage');
        const energyInput = document.getElementById('energy_mcal_kg');
        const fiberInput = document.getElementById('fiber_percentage');
        
        // Clear previous values
        proteinInput.placeholder = '';
        energyInput.placeholder = '';
        fiberInput.placeholder = '';
        
        if (category === 'concentrate') {
            proteinInput.placeholder = 'مثال: 18';
            energyInput.placeholder = 'مثال: 3.0';
            fiberInput.placeholder = 'مثال: 8';
        } else if (category === 'roughage') {
            proteinInput.placeholder = 'مثال: 12';
            energyInput.placeholder = 'مثال: 2.0';
            fiberInput.placeholder = 'مثال: 30';
        } else if (category === 'supplement') {
            proteinInput.placeholder = 'مثال: 35';
            energyInput.placeholder = 'مثال: 3.5';
            fiberInput.placeholder = 'مثال: 5';
        }
    });
});
</script>
{% endblock %}

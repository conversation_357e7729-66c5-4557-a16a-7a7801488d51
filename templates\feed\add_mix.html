{% extends "base.html" %}

{% block title %}إضافة خلطة علفية جديدة - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus me-2"></i>
                إضافة خلطة علفية جديدة
            </h2>
            <p class="text-muted mb-0">إنشاء خلطة علفية مخصصة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Add Mix Form -->
<div class="content-card">
    <form method="POST" id="mixForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="name" class="form-label">اسم الخلطة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                
                <div class="mb-3">
                    <label for="target_group" class="form-label">المجموعة المستهدفة <span class="text-danger">*</span></label>
                    <select class="form-select" id="target_group" name="target_group" required>
                        <option value="">اختر المجموعة</option>
                        <option value="lactating">أبقار حلوب</option>
                        <option value="dry">أبقار جافة</option>
                        <option value="pregnant">أبقار حامل</option>
                        <option value="young">عجول</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="recommended_daily_amount_kg" class="form-label">الكمية اليومية الموصى بها (كغ)</label>
                    <input type="number" step="0.1" class="form-control" id="recommended_daily_amount_kg" 
                           name="recommended_daily_amount_kg">
                </div>
            </div>
            
            <!-- Description and Instructions -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-file-text me-2"></i>
                    الوصف والتعليمات
                </h5>
                
                <div class="mb-3">
                    <label for="description" class="form-label">وصف الخلطة</label>
                    <textarea class="form-control" id="description" name="description" rows="3" 
                              placeholder="وصف مختصر للخلطة وأهدافها"></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="feeding_instructions" class="form-label">تعليمات التغذية</label>
                    <textarea class="form-control" id="feeding_instructions" name="feeding_instructions" rows="3" 
                              placeholder="تعليمات خاصة بالتغذية والتقديم"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Feed Components -->
        <div class="mb-4">
            <h5 class="mb-3">
                <i class="fas fa-list me-2"></i>
                مكونات الخلطة
            </h5>
            
            <div id="componentsContainer">
                <!-- Component rows will be added here -->
            </div>
            
            <button type="button" class="btn btn-outline-primary" onclick="addComponentRow()">
                <i class="fas fa-plus me-1"></i>
                إضافة مكون
            </button>
        </div>
        
        <!-- Nutritional Summary -->
        <div class="mb-4">
            <h5 class="mb-3">
                <i class="fas fa-chart-pie me-2"></i>
                ملخص القيم الغذائية
            </h5>
            
            <div class="row" id="nutritionalSummary">
                <div class="col-md-3">
                    <div class="stats-mini">
                        <div class="number" id="totalProtein">0.0</div>
                        <div class="label">البروتين (%)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-mini">
                        <div class="number" id="totalEnergy">0.0</div>
                        <div class="label">الطاقة (Mcal/kg)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-mini">
                        <div class="number" id="totalFiber">0.0</div>
                        <div class="label">الألياف (%)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-mini">
                        <div class="number" id="totalCost">0.000</div>
                        <div class="label">التكلفة (د.أ/كغ)</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('feed_mixes_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ الخلطة
            </button>
        </div>
    </form>
</div>

<!-- Available Components Reference -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-seedling me-2"></i>
        المكونات المتاحة
    </h5>
    <div class="row">
        {% for component in components %}
        <div class="col-md-4 col-sm-6 mb-2">
            <div class="d-flex align-items-center p-2 border rounded">
                <div class="me-2">
                    {% if component.category == 'concentrate' %}
                        <i class="fas fa-circle text-primary"></i>
                    {% elif component.category == 'roughage' %}
                        <i class="fas fa-circle text-success"></i>
                    {% else %}
                        <i class="fas fa-circle text-warning"></i>
                    {% endif %}
                </div>
                <div class="flex-grow-1">
                    <strong>{{ component.name }}</strong><br>
                    <small class="text-muted">
                        P: {{ "%.1f"|format(component.protein_percentage) }}% | 
                        E: {{ "%.2f"|format(component.energy_mcal_kg) }} | 
                        {{ "%.3f"|format(component.cost_per_kg_jod) }} د.أ/كغ
                    </small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let componentRowIndex = 0;
const components = {{ components_json | tojson }};

function addComponentRow() {
    const container = document.getElementById('componentsContainer');
    const rowHtml = `
        <div class="row mb-3 component-row" id="componentRow${componentRowIndex}">
            <div class="col-md-5">
                <select class="form-select" name="component_${componentRowIndex}_id" onchange="updateNutrition()">
                    <option value="">اختر المكون</option>
                    ${components.map(comp => `<option value="${comp.id}" data-protein="${comp.protein_percentage}" data-energy="${comp.energy_mcal_kg}" data-fiber="${comp.fiber_percentage}" data-cost="${comp.cost_per_kg_jod}">${comp.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <input type="number" step="0.1" class="form-control" name="component_${componentRowIndex}_quantity" 
                       placeholder="الكمية (كغ)" onchange="updateNutrition()">
            </div>
            <div class="col-md-2">
                <span class="form-control-plaintext percentage-display" id="percentage${componentRowIndex}">0%</span>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger" onclick="removeComponentRow(${componentRowIndex})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', rowHtml);
    componentRowIndex++;
}

function removeComponentRow(index) {
    const row = document.getElementById(`componentRow${index}`);
    if (row) {
        row.remove();
        updateNutrition();
    }
}

function updateNutrition() {
    let totalWeight = 0;
    let totalProtein = 0;
    let totalEnergy = 0;
    let totalFiber = 0;
    let totalCost = 0;
    
    const rows = document.querySelectorAll('.component-row');
    
    // Calculate total weight first
    rows.forEach(row => {
        const quantityInput = row.querySelector('input[type="number"]');
        const quantity = parseFloat(quantityInput.value) || 0;
        totalWeight += quantity;
    });
    
    // Calculate weighted averages
    rows.forEach((row, index) => {
        const select = row.querySelector('select');
        const quantityInput = row.querySelector('input[type="number"]');
        const percentageSpan = row.querySelector('.percentage-display');
        
        const quantity = parseFloat(quantityInput.value) || 0;
        const selectedOption = select.selectedOptions[0];
        
        if (selectedOption && quantity > 0) {
            const protein = parseFloat(selectedOption.dataset.protein) || 0;
            const energy = parseFloat(selectedOption.dataset.energy) || 0;
            const fiber = parseFloat(selectedOption.dataset.fiber) || 0;
            const cost = parseFloat(selectedOption.dataset.cost) || 0;
            
            const weightRatio = totalWeight > 0 ? quantity / totalWeight : 0;
            const percentage = (weightRatio * 100).toFixed(1);
            percentageSpan.textContent = percentage + '%';
            
            totalProtein += protein * weightRatio;
            totalEnergy += energy * weightRatio;
            totalFiber += fiber * weightRatio;
            totalCost += cost * quantity;
        } else {
            percentageSpan.textContent = '0%';
        }
    });
    
    // Update summary
    document.getElementById('totalProtein').textContent = totalProtein.toFixed(1);
    document.getElementById('totalEnergy').textContent = totalEnergy.toFixed(2);
    document.getElementById('totalFiber').textContent = totalFiber.toFixed(1);
    document.getElementById('totalCost').textContent = totalWeight > 0 ? (totalCost / totalWeight).toFixed(3) : '0.000';
}

// Add first component row on page load
document.addEventListener('DOMContentLoaded', function() {
    addComponentRow();
    
    // Form validation
    document.getElementById('mixForm').addEventListener('submit', function(e) {
        const rows = document.querySelectorAll('.component-row');
        let hasComponents = false;
        
        rows.forEach(row => {
            const select = row.querySelector('select');
            const quantityInput = row.querySelector('input[type="number"]');
            
            if (select.value && parseFloat(quantityInput.value) > 0) {
                hasComponents = true;
            }
        });
        
        if (!hasComponents) {
            alert('يجب إضافة مكون واحد على الأقل للخلطة');
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}سجلات إنتاج الحليب - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-glass-whiskey me-2"></i>
                سجلات إنتاج الحليب
            </h2>
            <p class="text-muted mb-0">تتبع ومراقبة إنتاج الحليب اليومي</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_milk_production') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                تسجيل إنتاج جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-6 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.1f"|format(today_total) }}</div>
            <div class="label">إنتاج اليوم (لتر)</div>
        </div>
    </div>
    <div class="col-lg-6 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ "%.1f"|format(week_total) }}</div>
            <div class="label">إنتاج الأسبوع (لتر)</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-3">
            <label for="date" class="form-label">التاريخ</label>
            <input type="date" class="form-control" id="date" name="date" value="{{ date_filter }}">
        </div>
        <div class="col-md-4">
            <label for="cow_id" class="form-label">البقرة</label>
            <select class="form-select" id="cow_id" name="cow_id">
                <option value="">جميع الأبقار</option>
                {% for cow in cows %}
                <option value="{{ cow.id }}" {% if cow_filter == cow.id|string %}selected{% endif %}>
                    {{ cow.tag_number }} - {{ cow.name or 'بدون اسم' }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('milk_production_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Production Records Table -->
<div class="content-card">
    {% if productions.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>البقرة</th>
                        <th>الصباح (لتر)</th>
                        <th>المساء (لتر)</th>
                        <th>الإجمالي (لتر)</th>
                        <th>نسبة الدهون</th>
                        <th>درجة الجودة</th>
                        <th>الحلاب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for production in productions.items %}
                    <tr>
                        <td>
                            <strong>{{ production.production_date.strftime('%Y-%m-%d') }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-cow text-primary me-2"></i>
                                <div>
                                    <strong>{{ production.cow.tag_number }}</strong><br>
                                    <small class="text-muted">{{ production.cow.name or 'بدون اسم' }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ "%.1f"|format(production.morning_quantity_liters) }}</td>
                        <td>{{ "%.1f"|format(production.evening_quantity_liters) }}</td>
                        <td>
                            <strong class="text-primary">{{ "%.1f"|format(production.total_quantity_liters) }}</strong>
                        </td>
                        <td>
                            {% if production.fat_percentage %}
                                {{ "%.1f"|format(production.fat_percentage) }}%
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if production.quality_grade == 'A' %}
                                <span class="badge bg-success">A - ممتاز</span>
                            {% elif production.quality_grade == 'B' %}
                                <span class="badge bg-warning">B - جيد</span>
                            {% elif production.quality_grade == 'C' %}
                                <span class="badge bg-danger">C - مقبول</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ production.quality_grade }}</span>
                            {% endif %}
                        </td>
                        <td>{{ production.milker_name or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('milk_production_details', production_id=production.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_milk_production', production_id=production.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('سجل إنتاج {{ production.production_date.strftime('%Y-%m-%d') }}', '{{ url_for('delete_milk_production', production_id=production.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if productions.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if productions.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('milk_production_list', page=productions.prev_num, date=date_filter, cow_id=cow_filter) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in productions.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != productions.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('milk_production_list', page=page_num, date=date_filter, cow_id=cow_filter) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if productions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('milk_production_list', page=productions.next_num, date=date_filter, cow_id=cow_filter) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-glass-whiskey fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد سجلات إنتاج</h5>
            <p class="text-muted">ابدأ بتسجيل أول إنتاج حليب</p>
            <a href="{{ url_for('add_milk_production') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                تسجيل إنتاج جديد
            </a>
        </div>
    {% endif %}
</div>


{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// Set today's date as default when page loads
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('date');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}

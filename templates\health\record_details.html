{% extends "base.html" %}

{% block title %}تفاصيل السجل الصحي - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-heartbeat me-2"></i>
                تفاصيل السجل الصحي
            </h2>
            <p class="text-muted mb-0">
                سجل رقم {{ record.id }} - {{ record.record_date.strftime('%Y-%m-%d') }}
            </p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('health_records_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Record Details -->
<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                المعلومات الأساسية
            </h5>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>البقرة:</strong></div>
                <div class="col-sm-8">
                    <a href="{{ url_for('cow_details', cow_id=record.cow.id) }}" class="text-decoration-none">
                        <i class="fas fa-cow me-1"></i>
                        {{ record.cow.tag_number }} - {{ record.cow.name or 'بدون اسم' }}
                    </a>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التاريخ:</strong></div>
                <div class="col-sm-8">{{ record.record_date.strftime('%Y-%m-%d') }}</div>
            </div>
            
            <div class="row mb-3">
                <div class="col-sm-4"><strong>نوع السجل:</strong></div>
                <div class="col-sm-8">
                    {% if record.record_type == 'vaccination' %}
                        <span class="badge bg-primary">تطعيم</span>
                    {% elif record.record_type == 'treatment' %}
                        <span class="badge bg-warning">علاج</span>
                    {% elif record.record_type == 'checkup' %}
                        <span class="badge bg-info">فحص</span>
                    {% elif record.record_type == 'surgery' %}
                        <span class="badge bg-danger">جراحة</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ record.record_type }}</span>
                    {% endif %}
                </div>
            </div>
            
            {% if record.condition_diagnosis %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التشخيص:</strong></div>
                <div class="col-sm-8">{{ record.condition_diagnosis }}</div>
            </div>
            {% endif %}
            
            {% if record.treatment_cost_jod %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>التكلفة:</strong></div>
                <div class="col-sm-8">
                    <span class="text-danger fw-bold">{{ "%.2f"|format(record.treatment_cost_jod) }} د.أ</span>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Medical Information -->
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-stethoscope me-2"></i>
                المعلومات الطبية
            </h5>
            
            {% if record.treatment_given %}
            <div class="mb-3">
                <strong>العلاج المُقدم:</strong>
                <p class="mt-1 mb-0">{{ record.treatment_given }}</p>
            </div>
            {% endif %}
            
            {% if record.medication_used %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الدواء:</strong></div>
                <div class="col-sm-8">{{ record.medication_used }}</div>
            </div>
            {% endif %}
            
            {% if record.dosage %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الجرعة:</strong></div>
                <div class="col-sm-8">{{ record.dosage }}</div>
            </div>
            {% endif %}
            
            {% if record.veterinarian_name %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>الطبيب البيطري:</strong></div>
                <div class="col-sm-8">
                    <i class="fas fa-user-md me-1"></i>
                    {{ record.veterinarian_name }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Follow-up Information -->
{% if record.follow_up_required or record.notes %}
<div class="row mt-4">
    {% if record.follow_up_required %}
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-calendar-check me-2"></i>
                معلومات المتابعة
            </h5>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>متابعة مطلوبة</strong>
                {% if record.follow_up_date %}
                    <br>تاريخ المتابعة: {{ record.follow_up_date.strftime('%Y-%m-%d') }}
                {% endif %}
            </div>
            
            {% if record.recovery_status %}
            <div class="row mb-3">
                <div class="col-sm-4"><strong>حالة الشفاء:</strong></div>
                <div class="col-sm-8">
                    {% if record.recovery_status == 'recovered' %}
                        <span class="badge bg-success">تم الشفاء</span>
                    {% elif record.recovery_status == 'ongoing' %}
                        <span class="badge bg-warning">قيد العلاج</span>
                    {% elif record.recovery_status == 'chronic' %}
                        <span class="badge bg-danger">حالة مزمنة</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ record.recovery_status }}</span>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% if record.notes %}
    <div class="col-md-6">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-sticky-note me-2"></i>
                ملاحظات
            </h5>
            <p class="mb-0">{{ record.notes }}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Record Timeline -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-clock me-2"></i>
                معلومات السجل
            </h5>
            
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-calendar-plus me-1"></i>
                        تم الإنشاء: {{ record.created_at.strftime('%Y-%m-%d %H:%M') if record.created_at else 'غير محدد' }}
                    </small>
                </div>
                <div class="col-md-6">
                    {% if record.follow_up_date %}
                    <small class="text-muted">
                        <i class="fas fa-calendar-check me-1"></i>
                        المتابعة المطلوبة: {{ record.follow_up_date.strftime('%Y-%m-%d') }}
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('health_records_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
            <div>
                {% if record.follow_up_required and record.follow_up_date %}
                <button class="btn btn-outline-warning me-2" disabled>
                    <i class="fas fa-calendar-plus me-1"></i>
                    إضافة متابعة
                </button>
                {% endif %}
                <a href="{{ url_for('edit_health_record', record_id=record.id) }}"
                   class="btn btn-outline-primary me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل السجل
                </a>
                <a href="{{ url_for('add_health_record') }}?cow_id={{ record.cow.id }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة سجل جديد لنفس البقرة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Related Records -->
<div class="row mt-4">
    <div class="col-12">
        <div class="content-card">
            <h5 class="mb-3">
                <i class="fas fa-history me-2"></i>
                السجلات الصحية الأخيرة لنفس البقرة
            </h5>
            
            {% if related_records %}
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>التشخيص</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for related_record in related_records %}
                        <tr>
                            <td>{{ related_record.record_date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if related_record.record_type == 'vaccination' %}
                                    <span class="badge bg-primary">تطعيم</span>
                                {% elif related_record.record_type == 'treatment' %}
                                    <span class="badge bg-warning">علاج</span>
                                {% elif related_record.record_type == 'checkup' %}
                                    <span class="badge bg-info">فحص</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ related_record.record_type }}</span>
                                {% endif %}
                            </td>
                            <td>{{ related_record.condition_diagnosis or '-' }}</td>
                            <td>
                                <a href="{{ url_for('health_record_details', record_id=related_record.id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted mb-0">لا توجد سجلات صحية أخرى لهذه البقرة</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

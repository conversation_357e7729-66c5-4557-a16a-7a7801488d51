# app.py - التطبيق الرئيسي المتكامل
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta, date
from decimal import Decimal
import json
import os
from models import db, User, Cow, MilkProduction, HealthRecord, FeedComponent, FeedMix, FeedMixComponent, Expense, Revenue, Inventory

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///dairy_farm.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# دوال مساعدة للقوالب
@app.template_filter('age_from_birth')
def age_from_birth(birth_date):
    """حساب العمر من تاريخ الولادة"""
    if not birth_date:
        return '-'

    today = date.today()
    age_days = (today - birth_date).days

    if age_days < 30:
        return f"{age_days} يوم"
    elif age_days < 365:
        months = round(age_days / 30, 1)
        return f"{months} شهر"
    else:
        years = round(age_days / 365, 1)
        return f"{years} سنة"

@app.template_global()
def today():
    """إرجاع تاريخ اليوم"""
    return date.today()

def calculate_feed_mix_nutrition(components_data):
    """حساب القيم الغذائية للخلطة العلفية"""
    total_weight = sum(comp['quantity_kg'] for comp in components_data)
    total_protein = 0
    total_energy = 0
    total_fiber = 0
    total_fat = 0
    total_cost = 0

    for comp_data in components_data:
        component = FeedComponent.query.get(comp_data['component_id'])
        if component:
            weight_ratio = comp_data['quantity_kg'] / total_weight
            total_protein += component.protein_percentage * weight_ratio
            total_energy += component.energy_mcal_kg * weight_ratio
            total_fiber += component.fiber_percentage * weight_ratio
            total_fat += (component.fat_percentage or 0) * weight_ratio
            total_cost += component.cost_per_kg_jod * comp_data['quantity_kg']

    return {
        'protein_percentage': total_protein,
        'energy_mcal_kg': total_energy,
        'fiber_percentage': total_fiber,
        'fat_percentage': total_fat,
        'cost_per_kg_jod': total_cost / total_weight if total_weight > 0 else 0,
        'total_weight_kg': total_weight
    }

# دالة مساعدة لحساب القيم الغذائية للخلطة
def calculate_feed_mix_nutrition(components_data):
    """
    حساب القيم الغذائية للخلطة العلفية
    
    Args:
        components_data: قائمة بالمكونات وكمياتها
    
    Returns:
        dict: القيم الغذائية المحسوبة
    """
    total_weight = 0
    total_protein = 0
    total_energy = 0
    total_fiber = 0
    total_fat = 0
    total_cost = 0
    
    for comp_data in components_data:
        component = FeedComponent.query.get(comp_data['component_id'])
        if not component:
            continue
            
        quantity = float(comp_data['quantity_kg'])
        total_weight += quantity
        
        # حساب المساهمة في كل عنصر غذائي
        total_protein += (component.protein_percentage * quantity / 100)
        total_energy += (component.energy_mcal_kg * quantity)
        total_fiber += (component.fiber_percentage * quantity / 100)
        total_fat += (component.fat_percentage * quantity / 100)
        total_cost += (component.cost_per_kg_jod * quantity)
    
    if total_weight == 0:
        return {
            'total_weight_kg': 0,
            'protein_percentage': 0,
            'energy_mcal_kg': 0,
            'fiber_percentage': 0,
            'fat_percentage': 0,
            'cost_per_kg_jod': 0,
            'total_cost_jod': 0
        }
    
    return {
        'total_weight_kg': total_weight,
        'protein_percentage': (total_protein / total_weight) * 100,
        'energy_mcal_kg': total_energy / total_weight,
        'fiber_percentage': (total_fiber / total_weight) * 100,
        'fat_percentage': (total_fat / total_weight) * 100,
        'cost_per_kg_jod': total_cost / total_weight,
        'total_cost_jod': total_cost
    }

# الصفحة الرئيسية - لوحة التحكم المتقدمة
@app.route('/')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية مع إحصائيات شاملة"""
    today = date.today()
    
    # إحصائيات إنتاج الحليب
    today_milk = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date == today
    ).scalar() or 0
    
    # إحصائيات الأسبوع الحالي
    week_start = today - timedelta(days=today.weekday())
    week_milk = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date >= week_start
    ).scalar() or 0

    # إحصائيات الشهر الحالي
    month_start = today.replace(day=1)
    month_milk = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date >= month_start
    ).scalar() or 0

    # عدد الأبقار
    total_cows = Cow.query.filter_by(is_active=True).count()
    active_cows = Cow.query.filter_by(is_active=True, status='active').count()

    # متوسط الإنتاج اليومي
    avg_daily_production = db.session.query(db.func.avg(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date >= week_start
    ).scalar() or 0

    # الإحصائيات المالية
    month_start = today.replace(day=1)

    # إيرادات الشهر
    month_revenues = db.session.query(db.func.sum(Revenue.amount_jod)).filter(
        Revenue.sale_date >= month_start
    ).scalar() or 0

    # مصروفات الشهر
    month_expenses = db.session.query(db.func.sum(Expense.amount_jod)).filter(
        Expense.expense_date >= month_start
    ).scalar() or 0

    # صافي الربح
    net_profit = month_revenues - month_expenses

    return render_template('dashboard.html',
                         today_milk=today_milk,
                         week_milk=week_milk,
                         month_milk=month_milk,
                         total_cows=total_cows,
                         active_cows=active_cows,
                         avg_daily_production=avg_daily_production,
                         month_revenues=month_revenues,
                         month_expenses=month_expenses,
                         net_profit=net_profit)

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# ===== إدارة الأبقار =====

@app.route('/cows')
@login_required
def cows_list():
    """عرض قائمة الأبقار"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    status_filter = request.args.get('status', '', type=str)

    query = Cow.query.filter_by(is_active=True)

    if search:
        query = query.filter(
            db.or_(
                Cow.tag_number.contains(search),
                Cow.name.contains(search),
                Cow.breed.contains(search)
            )
        )

    if status_filter:
        query = query.filter_by(status=status_filter)

    cows = query.order_by(Cow.tag_number).paginate(
        page=page, per_page=20, error_out=False
    )

    # إحصائيات سريعة
    total_cows = Cow.query.filter_by(is_active=True).count()
    active_cows = Cow.query.filter_by(is_active=True, status='active').count()
    pregnant_cows = Cow.query.filter_by(is_active=True, status='pregnant').count()
    dry_cows = Cow.query.filter_by(is_active=True, status='dry').count()

    return render_template('cows/list.html',
                         cows=cows,
                         search=search,
                         status_filter=status_filter,
                         total_cows=total_cows,
                         active_cows=active_cows,
                         pregnant_cows=pregnant_cows,
                         dry_cows=dry_cows)

@app.route('/cows/add', methods=['GET', 'POST'])
@login_required
def add_cow():
    """إضافة بقرة جديدة"""
    if request.method == 'POST':
        try:
            cow = Cow(
                tag_number=request.form['tag_number'],
                name=request.form.get('name'),
                breed=request.form.get('breed'),
                birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form.get('birth_date') else None,
                purchase_date=datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form.get('purchase_date') else None,
                purchase_price_jod=float(request.form['purchase_price_jod']) if request.form.get('purchase_price_jod') else None,
                purchase_weight_kg=float(request.form['purchase_weight_kg']) if request.form.get('purchase_weight_kg') else None,
                current_weight_kg=float(request.form['current_weight_kg']) if request.form.get('current_weight_kg') else None,
                status=request.form.get('status', 'active'),
                sire_name=request.form.get('sire_name'),
                dam_name=request.form.get('dam_name'),
                notes=request.form.get('notes')
            )

            db.session.add(cow)
            db.session.commit()
            flash('تم إضافة البقرة بنجاح', 'success')
            return redirect(url_for('cows_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('cows/add.html')

@app.route('/cows/<int:cow_id>')
@login_required
def cow_details(cow_id):
    """عرض تفاصيل البقرة"""
    cow = Cow.query.get_or_404(cow_id)

    # إحصائيات إنتاج الحليب
    today = date.today()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)

    # إنتاج اليوم
    today_production = MilkProduction.query.filter_by(
        cow_id=cow_id, production_date=today
    ).first()

    # إنتاج الأسبوع
    week_production = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.cow_id == cow_id,
        MilkProduction.production_date >= week_start
    ).scalar() or 0

    # إنتاج الشهر
    month_production = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.cow_id == cow_id,
        MilkProduction.production_date >= month_start
    ).scalar() or 0

    # متوسط الإنتاج اليومي
    avg_production = db.session.query(db.func.avg(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.cow_id == cow_id,
        MilkProduction.production_date >= week_start
    ).scalar() or 0

    # آخر السجلات الصحية
    recent_health_records = HealthRecord.query.filter_by(cow_id=cow_id).order_by(
        HealthRecord.record_date.desc()
    ).limit(5).all()

    return render_template('cows/details.html',
                         cow=cow,
                         today_production=today_production,
                         week_production=week_production,
                         month_production=month_production,
                         avg_production=avg_production,
                         recent_health_records=recent_health_records)

@app.route('/cows/<int:cow_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_cow(cow_id):
    """تعديل بيانات البقرة"""
    cow = Cow.query.get_or_404(cow_id)

    if request.method == 'POST':
        try:
            cow.tag_number = request.form['tag_number']
            cow.name = request.form.get('name')
            cow.breed = request.form.get('breed')
            cow.birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form.get('birth_date') else None
            cow.purchase_date = datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form.get('purchase_date') else None
            cow.purchase_price_jod = float(request.form['purchase_price_jod']) if request.form.get('purchase_price_jod') else None
            cow.purchase_weight_kg = float(request.form['purchase_weight_kg']) if request.form.get('purchase_weight_kg') else None
            cow.current_weight_kg = float(request.form['current_weight_kg']) if request.form.get('current_weight_kg') else None
            cow.status = request.form.get('status', 'active')
            cow.body_condition_score = float(request.form['body_condition_score']) if request.form.get('body_condition_score') else None
            cow.last_breeding_date = datetime.strptime(request.form['last_breeding_date'], '%Y-%m-%d').date() if request.form.get('last_breeding_date') else None
            cow.expected_calving_date = datetime.strptime(request.form['expected_calving_date'], '%Y-%m-%d').date() if request.form.get('expected_calving_date') else None
            cow.total_calvings = int(request.form['total_calvings']) if request.form.get('total_calvings') else 0
            cow.sire_name = request.form.get('sire_name')
            cow.dam_name = request.form.get('dam_name')
            cow.notes = request.form.get('notes')
            cow.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث بيانات البقرة بنجاح', 'success')
            return redirect(url_for('cow_details', cow_id=cow_id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('cows/edit.html', cow=cow)

@app.route('/cows/<int:cow_id>/delete', methods=['POST'])
@login_required
def delete_cow(cow_id):
    """حذف البقرة (حذف منطقي)"""
    cow = Cow.query.get_or_404(cow_id)

    try:
        # حذف منطقي - تعيين is_active إلى False
        cow.is_active = False
        cow.updated_at = datetime.utcnow()

        db.session.commit()
        flash(f'تم حذف البقرة {cow.tag_number} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('cows_list'))

# ===== إدارة إنتاج الحليب =====

@app.route('/milk-production')
@login_required
def milk_production_list():
    """عرض قائمة إنتاج الحليب"""
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date', '', type=str)
    cow_filter = request.args.get('cow_id', '', type=str)

    query = MilkProduction.query.join(Cow)

    if date_filter:
        filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
        query = query.filter(MilkProduction.production_date == filter_date)

    if cow_filter:
        query = query.filter(MilkProduction.cow_id == int(cow_filter))

    productions = query.order_by(MilkProduction.production_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # قائمة الأبقار للفلترة
    cows = Cow.query.filter_by(is_active=True).order_by(Cow.tag_number).all()

    # إحصائيات سريعة
    today = date.today()
    today_total = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date == today
    ).scalar() or 0

    week_start = today - timedelta(days=today.weekday())
    week_total = db.session.query(db.func.sum(MilkProduction.total_quantity_liters)).filter(
        MilkProduction.production_date >= week_start
    ).scalar() or 0

    return render_template('milk_production/list.html',
                         productions=productions,
                         cows=cows,
                         date_filter=date_filter,
                         cow_filter=cow_filter,
                         today_total=today_total,
                         week_total=week_total)

@app.route('/milk-production/add', methods=['GET', 'POST'])
@login_required
def add_milk_production():
    """إضافة سجل إنتاج حليب جديد"""
    if request.method == 'POST':
        try:
            morning_qty = float(request.form.get('morning_quantity_liters', 0))
            evening_qty = float(request.form.get('evening_quantity_liters', 0))
            total_qty = morning_qty + evening_qty

            production = MilkProduction(
                cow_id=int(request.form['cow_id']),
                production_date=datetime.strptime(request.form['production_date'], '%Y-%m-%d').date(),
                morning_quantity_liters=morning_qty,
                evening_quantity_liters=evening_qty,
                total_quantity_liters=total_qty,
                fat_percentage=float(request.form['fat_percentage']) if request.form.get('fat_percentage') else None,
                protein_percentage=float(request.form['protein_percentage']) if request.form.get('protein_percentage') else None,
                somatic_cell_count=int(request.form['somatic_cell_count']) if request.form.get('somatic_cell_count') else None,
                quality_grade=request.form.get('quality_grade', 'A'),
                milker_name=request.form.get('milker_name'),
                temperature_celsius=float(request.form['temperature_celsius']) if request.form.get('temperature_celsius') else None,
                notes=request.form.get('notes')
            )

            db.session.add(production)
            db.session.commit()
            flash('تم إضافة سجل الإنتاج بنجاح', 'success')
            return redirect(url_for('milk_production_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    # قائمة الأبقار النشطة
    cows = Cow.query.filter_by(is_active=True, status='active').order_by(Cow.tag_number).all()
    return render_template('milk_production/add.html', cows=cows)

@app.route('/milk-production/<int:production_id>')
@login_required
def milk_production_details(production_id):
    """عرض تفاصيل إنتاج الحليب"""
    production = MilkProduction.query.get_or_404(production_id)

    # البحث عن سجلات الإنتاج الأخرى لنفس البقرة في نفس الأسبوع
    from datetime import timedelta
    week_start = production.production_date - timedelta(days=production.production_date.weekday())
    week_end = week_start + timedelta(days=6)

    weekly_productions = MilkProduction.query.filter(
        MilkProduction.cow_id == production.cow_id,
        MilkProduction.production_date >= week_start,
        MilkProduction.production_date <= week_end,
        MilkProduction.id != production_id
    ).order_by(MilkProduction.production_date.desc()).all()

    # حساب متوسط الإنتاج الأسبوعي
    weekly_total = sum([p.total_quantity_liters for p in weekly_productions]) + production.total_quantity_liters
    weekly_average = weekly_total / (len(weekly_productions) + 1) if weekly_productions else production.total_quantity_liters

    # البحث عن آخر 5 سجلات إنتاج لنفس البقرة
    recent_productions = MilkProduction.query.filter(
        MilkProduction.cow_id == production.cow_id,
        MilkProduction.id != production_id
    ).order_by(MilkProduction.production_date.desc()).limit(5).all()

    return render_template('milk_production/production_details.html',
                         production=production,
                         weekly_productions=weekly_productions,
                         weekly_average=weekly_average,
                         recent_productions=recent_productions)

@app.route('/milk-production/<int:production_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_milk_production(production_id):
    """تعديل سجل إنتاج الحليب"""
    production = MilkProduction.query.get_or_404(production_id)

    if request.method == 'POST':
        try:
            morning_qty = float(request.form.get('morning_quantity_liters', 0))
            evening_qty = float(request.form.get('evening_quantity_liters', 0))
            total_qty = morning_qty + evening_qty

            production.cow_id = int(request.form['cow_id'])
            production.production_date = datetime.strptime(request.form['production_date'], '%Y-%m-%d').date()
            production.morning_quantity_liters = morning_qty
            production.evening_quantity_liters = evening_qty
            production.total_quantity_liters = total_qty
            production.fat_percentage = float(request.form['fat_percentage']) if request.form.get('fat_percentage') else None
            production.protein_percentage = float(request.form['protein_percentage']) if request.form.get('protein_percentage') else None
            production.somatic_cell_count = int(request.form['somatic_cell_count']) if request.form.get('somatic_cell_count') else None
            production.quality_grade = request.form.get('quality_grade', 'A')
            production.milker_name = request.form.get('milker_name')
            production.temperature_celsius = float(request.form['temperature_celsius']) if request.form.get('temperature_celsius') else None
            production.notes = request.form.get('notes')

            db.session.commit()
            flash('تم تحديث سجل الإنتاج بنجاح', 'success')
            return redirect(url_for('milk_production_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    # قائمة الأبقار النشطة
    cows = Cow.query.filter_by(is_active=True, status='active').order_by(Cow.tag_number).all()
    return render_template('milk_production/edit.html', production=production, cows=cows)

@app.route('/milk-production/<int:production_id>/delete', methods=['POST'])
@login_required
def delete_milk_production(production_id):
    """حذف سجل إنتاج الحليب"""
    production = MilkProduction.query.get_or_404(production_id)

    try:
        db.session.delete(production)
        db.session.commit()
        flash('تم حذف سجل الإنتاج بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('milk_production_list'))

# ===== إدارة العلف والتغذية =====

@app.route('/feed-components')
@login_required
def feed_components_list():
    """عرض قائمة مكونات العلف"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    category_filter = request.args.get('category', '', type=str)

    query = FeedComponent.query.filter_by(is_active=True)

    if search:
        query = query.filter(
            db.or_(
                FeedComponent.name.contains(search),
                FeedComponent.name_english.contains(search),
                FeedComponent.supplier.contains(search)
            )
        )

    if category_filter:
        query = query.filter_by(category=category_filter)

    components = query.order_by(FeedComponent.name).paginate(
        page=page, per_page=20, error_out=False
    )

    # إحصائيات سريعة
    total_components = FeedComponent.query.filter_by(is_active=True).count()
    concentrate_count = FeedComponent.query.filter_by(is_active=True, category='concentrate').count()
    roughage_count = FeedComponent.query.filter_by(is_active=True, category='roughage').count()
    supplement_count = FeedComponent.query.filter_by(is_active=True, category='supplement').count()

    return render_template('feed/components_list.html',
                         components=components,
                         search=search,
                         category_filter=category_filter,
                         total_components=total_components,
                         concentrate_count=concentrate_count,
                         roughage_count=roughage_count,
                         supplement_count=supplement_count)

@app.route('/feed-components/add', methods=['GET', 'POST'])
@login_required
def add_feed_component():
    """إضافة مكون علف جديد"""
    if request.method == 'POST':
        try:
            component = FeedComponent(
                name=request.form['name'],
                name_english=request.form.get('name_english'),
                category=request.form['category'],
                protein_percentage=float(request.form['protein_percentage']),
                energy_mcal_kg=float(request.form['energy_mcal_kg']),
                fiber_percentage=float(request.form['fiber_percentage']),
                fat_percentage=float(request.form.get('fat_percentage', 0)),
                ash_percentage=float(request.form.get('ash_percentage', 0)),
                moisture_percentage=float(request.form.get('moisture_percentage', 0)),
                calcium_percentage=float(request.form.get('calcium_percentage', 0)),
                phosphorus_percentage=float(request.form.get('phosphorus_percentage', 0)),
                cost_per_kg_jod=float(request.form['cost_per_kg_jod']),
                supplier=request.form.get('supplier'),
                supplier_phone=request.form.get('supplier_phone'),
                description=request.form.get('description'),
                storage_requirements=request.form.get('storage_requirements'),
                shelf_life_days=int(request.form.get('shelf_life_days', 30))
            )

            db.session.add(component)
            db.session.commit()
            flash('تم إضافة مكون العلف بنجاح', 'success')
            return redirect(url_for('feed_components_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('feed/add_component.html')

@app.route('/feed-components/<int:component_id>')
@login_required
def feed_component_details(component_id):
    """عرض تفاصيل مكون العلف"""
    component = FeedComponent.query.get_or_404(component_id)

    # البحث عن الخلطات التي تستخدم هذا المكون
    mixes_using_component = db.session.query(FeedMix).join(FeedMixComponent).filter(
        FeedMixComponent.component_id == component_id,
        FeedMix.is_active == True
    ).all()

    return render_template('feed/component_details.html',
                         component=component,
                         mixes_using_component=mixes_using_component)

@app.route('/feed-components/<int:component_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_feed_component(component_id):
    """تعديل مكون العلف"""
    component = FeedComponent.query.get_or_404(component_id)

    if request.method == 'POST':
        try:
            component.name = request.form['name']
            component.name_english = request.form.get('name_english')
            component.category = request.form['category']
            component.protein_percentage = float(request.form['protein_percentage'])
            component.energy_mcal_kg = float(request.form['energy_mcal_kg'])
            component.fiber_percentage = float(request.form['fiber_percentage'])
            component.fat_percentage = float(request.form.get('fat_percentage', 0))
            component.ash_percentage = float(request.form.get('ash_percentage', 0))
            component.moisture_percentage = float(request.form.get('moisture_percentage', 0))
            component.calcium_percentage = float(request.form.get('calcium_percentage', 0))
            component.phosphorus_percentage = float(request.form.get('phosphorus_percentage', 0))
            component.cost_per_kg_jod = float(request.form['cost_per_kg_jod'])
            component.supplier = request.form.get('supplier')
            component.supplier_phone = request.form.get('supplier_phone')
            component.description = request.form.get('description')
            component.storage_requirements = request.form.get('storage_requirements')
            component.shelf_life_days = int(request.form.get('shelf_life_days', 30))
            component.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث مكون العلف بنجاح', 'success')
            return redirect(url_for('feed_components_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('feed/edit_component.html', component=component)

@app.route('/feed-components/<int:component_id>/delete', methods=['POST'])
@login_required
def delete_feed_component(component_id):
    """حذف مكون العلف (حذف منطقي)"""
    component = FeedComponent.query.get_or_404(component_id)

    try:
        # التحقق من عدم استخدام المكون في خلطات نشطة
        active_mixes = FeedMixComponent.query.join(FeedMix).filter(
            FeedMixComponent.component_id == component_id,
            FeedMix.is_active == True
        ).count()

        if active_mixes > 0:
            flash(f'لا يمكن حذف المكون لأنه مستخدم في {active_mixes} خلطة علفية نشطة', 'error')
            return redirect(url_for('feed_components_list'))

        # حذف منطقي
        component.is_active = False
        component.updated_at = datetime.utcnow()

        db.session.commit()
        flash(f'تم حذف مكون العلف "{component.name}" بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('feed_components_list'))

@app.route('/feed-mixes')
@login_required
def feed_mixes_list():
    """عرض قائمة الخلطات العلفية"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    target_filter = request.args.get('target_group', '', type=str)

    query = FeedMix.query.filter_by(is_active=True)

    if search:
        query = query.filter(
            db.or_(
                FeedMix.name.contains(search),
                FeedMix.description.contains(search)
            )
        )

    if target_filter:
        query = query.filter_by(target_group=target_filter)

    mixes = query.order_by(FeedMix.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # إحصائيات سريعة
    total_mixes = FeedMix.query.filter_by(is_active=True).count()
    lactating_mixes = FeedMix.query.filter_by(is_active=True, target_group='lactating').count()
    dry_mixes = FeedMix.query.filter_by(is_active=True, target_group='dry').count()

    return render_template('feed/mixes_list.html',
                         mixes=mixes,
                         search=search,
                         target_filter=target_filter,
                         total_mixes=total_mixes,
                         lactating_mixes=lactating_mixes,
                         dry_mixes=dry_mixes)

@app.route('/feed-mixes/add', methods=['GET', 'POST'])
@login_required
def add_feed_mix():
    """إضافة خلطة علفية جديدة"""
    if request.method == 'POST':
        try:
            # إنشاء الخلطة الأساسية
            mix = FeedMix(
                name=request.form['name'],
                description=request.form.get('description'),
                target_group=request.form['target_group'],
                feeding_instructions=request.form.get('feeding_instructions'),
                recommended_daily_amount_kg=float(request.form.get('recommended_daily_amount_kg', 0)),
                created_by=current_user.id
            )

            db.session.add(mix)
            db.session.flush()  # للحصول على ID الخلطة

            # إضافة مكونات الخلطة
            components_data = []
            total_weight = 0

            for key in request.form.keys():
                if key.startswith('component_') and key.endswith('_id'):
                    index = key.split('_')[1]
                    component_id = int(request.form[f'component_{index}_id'])
                    quantity = float(request.form[f'component_{index}_quantity'])

                    if component_id and quantity > 0:
                        components_data.append({
                            'component_id': component_id,
                            'quantity_kg': quantity
                        })
                        total_weight += quantity

            # حساب النسب المئوية وإضافة المكونات
            for comp_data in components_data:
                percentage = (comp_data['quantity_kg'] / total_weight) * 100

                mix_component = FeedMixComponent(
                    feed_mix_id=mix.id,
                    component_id=comp_data['component_id'],
                    quantity_kg=comp_data['quantity_kg'],
                    percentage_in_mix=percentage
                )
                db.session.add(mix_component)

            # حساب القيم الغذائية للخلطة
            nutrition = calculate_feed_mix_nutrition(components_data)
            mix.total_protein_percentage = nutrition['protein_percentage']
            mix.total_energy_mcal_kg = nutrition['energy_mcal_kg']
            mix.total_fiber_percentage = nutrition['fiber_percentage']
            mix.total_fat_percentage = nutrition['fat_percentage']
            mix.total_cost_per_kg_jod = nutrition['cost_per_kg_jod']
            mix.total_weight_kg = nutrition['total_weight_kg']

            db.session.commit()
            flash('تم إضافة الخلطة العلفية بنجاح', 'success')
            return redirect(url_for('feed_mixes_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    # قائمة مكونات العلف المتاحة
    components_query = FeedComponent.query.filter_by(is_active=True).order_by(FeedComponent.name).all()

    # تحويل كائنات FeedComponent إلى قواميس للاستخدام في JavaScript
    components_json = []
    for component in components_query:
        components_json.append({
            'id': component.id,
            'name': component.name,
            'name_english': component.name_english,
            'category': component.category,
            'protein_percentage': component.protein_percentage,
            'energy_mcal_kg': component.energy_mcal_kg,
            'fiber_percentage': component.fiber_percentage,
            'fat_percentage': component.fat_percentage or 0,
            'cost_per_kg_jod': component.cost_per_kg_jod
        })

    return render_template('feed/add_mix.html',
                         components=components_query,
                         components_json=components_json)

@app.route('/feed-mixes/<int:mix_id>')
@login_required
def feed_mix_details(mix_id):
    """عرض تفاصيل الخلطة العلفية"""
    mix = FeedMix.query.get_or_404(mix_id)

    # حساب القيم الغذائية الإجمالية للخلطة
    total_protein = 0
    total_energy = 0
    total_fiber = 0
    total_cost = 0

    for mix_component in mix.components:
        component = mix_component.component
        percentage = mix_component.percentage / 100

        total_protein += component.protein_percentage * percentage
        total_energy += component.energy_mcal_kg * percentage
        total_fiber += component.fiber_percentage * percentage
        total_cost += component.cost_per_kg_jod * percentage

    nutritional_summary = {
        'protein': total_protein,
        'energy': total_energy,
        'fiber': total_fiber,
        'cost_per_kg': total_cost
    }

    return render_template('feed/mix_details.html',
                         mix=mix,
                         nutritional_summary=nutritional_summary)

@app.route('/feed-mixes/<int:mix_id>/delete', methods=['POST'])
@login_required
def delete_feed_mix(mix_id):
    """حذف الخلطة العلفية (حذف منطقي)"""
    mix = FeedMix.query.get_or_404(mix_id)

    try:
        # حذف منطقي
        mix.is_active = False
        mix.updated_at = datetime.utcnow()

        db.session.commit()
        flash(f'تم حذف الخلطة العلفية "{mix.name}" بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('feed_mixes_list'))

# ===== السجلات الصحية =====

@app.route('/health-records')
@login_required
def health_records_list():
    """عرض قائمة السجلات الصحية"""
    page = request.args.get('page', 1, type=int)
    cow_filter = request.args.get('cow_id', '', type=str)
    record_type_filter = request.args.get('record_type', '', type=str)
    date_from = request.args.get('date_from', '', type=str)
    date_to = request.args.get('date_to', '', type=str)

    query = HealthRecord.query.join(Cow)

    if cow_filter:
        query = query.filter(HealthRecord.cow_id == int(cow_filter))

    if record_type_filter:
        query = query.filter(HealthRecord.record_type == record_type_filter)

    if date_from:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        query = query.filter(HealthRecord.record_date >= from_date)

    if date_to:
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        query = query.filter(HealthRecord.record_date <= to_date)

    records = query.order_by(HealthRecord.record_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # قائمة الأبقار للفلترة
    cows = Cow.query.filter_by(is_active=True).order_by(Cow.tag_number).all()

    # إحصائيات سريعة
    total_records = HealthRecord.query.count()
    vaccination_records = HealthRecord.query.filter_by(record_type='vaccination').count()
    treatment_records = HealthRecord.query.filter_by(record_type='treatment').count()
    checkup_records = HealthRecord.query.filter_by(record_type='checkup').count()

    return render_template('health/records_list.html',
                         records=records,
                         cows=cows,
                         cow_filter=cow_filter,
                         record_type_filter=record_type_filter,
                         date_from=date_from,
                         date_to=date_to,
                         total_records=total_records,
                         vaccination_records=vaccination_records,
                         treatment_records=treatment_records,
                         checkup_records=checkup_records)

@app.route('/health-records/add', methods=['GET', 'POST'])
@login_required
def add_health_record():
    """إضافة سجل صحي جديد"""
    if request.method == 'POST':
        try:
            record = HealthRecord(
                cow_id=int(request.form['cow_id']),
                record_date=datetime.strptime(request.form['record_date'], '%Y-%m-%d').date(),
                record_type=request.form['record_type'],
                condition_diagnosis=request.form.get('condition_diagnosis'),
                treatment_given=request.form.get('treatment_given'),
                medication_used=request.form.get('medication_used'),
                dosage=request.form.get('dosage'),
                veterinarian_name=request.form.get('veterinarian_name'),
                treatment_cost_jod=float(request.form['treatment_cost_jod']) if request.form.get('treatment_cost_jod') else None,
                follow_up_required=request.form.get('follow_up_required') == 'on',
                follow_up_date=datetime.strptime(request.form['follow_up_date'], '%Y-%m-%d').date() if request.form.get('follow_up_date') else None,
                notes=request.form.get('notes')
            )

            db.session.add(record)
            db.session.commit()
            flash('تم إضافة السجل الصحي بنجاح', 'success')
            return redirect(url_for('health_records_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    # قائمة الأبقار
    cows = Cow.query.filter_by(is_active=True).order_by(Cow.tag_number).all()
    return render_template('health/add_record.html', cows=cows)

@app.route('/health-records/<int:record_id>')
@login_required
def health_record_details(record_id):
    """عرض تفاصيل السجل الصحي"""
    record = HealthRecord.query.get_or_404(record_id)

    # البحث عن السجلات الصحية الأخرى لنفس البقرة
    related_records = HealthRecord.query.filter(
        HealthRecord.cow_id == record.cow_id,
        HealthRecord.id != record_id
    ).order_by(HealthRecord.record_date.desc()).limit(5).all()

    return render_template('health/record_details.html',
                         record=record,
                         related_records=related_records)

@app.route('/health-records/<int:record_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_health_record(record_id):
    """تعديل السجل الصحي"""
    record = HealthRecord.query.get_or_404(record_id)

    if request.method == 'POST':
        try:
            record.cow_id = int(request.form['cow_id'])
            record.record_date = datetime.strptime(request.form['record_date'], '%Y-%m-%d').date()
            record.record_type = request.form['record_type']
            record.condition_diagnosis = request.form.get('condition_diagnosis')
            record.treatment_given = request.form.get('treatment_given')
            record.medication_used = request.form.get('medication_used')
            record.dosage = request.form.get('dosage')
            record.veterinarian_name = request.form.get('veterinarian_name')
            record.treatment_cost_jod = float(request.form['treatment_cost_jod']) if request.form.get('treatment_cost_jod') else None
            record.follow_up_required = request.form.get('follow_up_required') == 'on'
            record.follow_up_date = datetime.strptime(request.form['follow_up_date'], '%Y-%m-%d').date() if request.form.get('follow_up_date') else None
            record.notes = request.form.get('notes')

            db.session.commit()
            flash('تم تحديث السجل الصحي بنجاح', 'success')
            return redirect(url_for('health_record_details', record_id=record_id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    # قائمة الأبقار
    cows = Cow.query.filter_by(is_active=True).order_by(Cow.tag_number).all()
    return render_template('health/edit_record.html', record=record, cows=cows)

@app.route('/health-records/<int:record_id>/delete', methods=['POST'])
@login_required
def delete_health_record(record_id):
    """حذف السجل الصحي"""
    record = HealthRecord.query.get_or_404(record_id)

    try:
        db.session.delete(record)
        db.session.commit()
        flash('تم حذف السجل الصحي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('health_records_list'))

# ===== إدارة المصروفات والإيرادات =====

@app.route('/expenses')
@login_required
def expenses_list():
    """عرض قائمة المصروفات"""
    page = request.args.get('page', 1, type=int)
    category_filter = request.args.get('category', '', type=str)
    date_from = request.args.get('date_from', '', type=str)
    date_to = request.args.get('date_to', '', type=str)

    query = Expense.query

    if category_filter:
        query = query.filter(Expense.category == category_filter)

    if date_from:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        query = query.filter(Expense.expense_date >= from_date)

    if date_to:
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        query = query.filter(Expense.expense_date <= to_date)

    expenses = query.order_by(Expense.expense_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # إحصائيات سريعة
    today = date.today()
    month_start = today.replace(day=1)

    total_expenses = db.session.query(db.func.sum(Expense.amount_jod)).scalar() or 0
    month_expenses = db.session.query(db.func.sum(Expense.amount_jod)).filter(
        Expense.expense_date >= month_start
    ).scalar() or 0

    feed_expenses = db.session.query(db.func.sum(Expense.amount_jod)).filter(
        Expense.category == 'feed',
        Expense.expense_date >= month_start
    ).scalar() or 0

    veterinary_expenses = db.session.query(db.func.sum(Expense.amount_jod)).filter(
        Expense.category == 'veterinary',
        Expense.expense_date >= month_start
    ).scalar() or 0

    return render_template('finance/expenses_list.html',
                         expenses=expenses,
                         category_filter=category_filter,
                         date_from=date_from,
                         date_to=date_to,
                         total_expenses=total_expenses,
                         month_expenses=month_expenses,
                         feed_expenses=feed_expenses,
                         veterinary_expenses=veterinary_expenses)

@app.route('/expenses/add', methods=['GET', 'POST'])
@login_required
def add_expense():
    """إضافة مصروف جديد"""
    if request.method == 'POST':
        try:
            expense = Expense(
                category=request.form['category'],
                subcategory=request.form.get('subcategory'),
                description=request.form['description'],
                amount_jod=float(request.form['amount_jod']),
                quantity=float(request.form['quantity']) if request.form.get('quantity') else None,
                unit_price_jod=float(request.form['unit_price_jod']) if request.form.get('unit_price_jod') else None,
                unit=request.form.get('unit'),
                expense_date=datetime.strptime(request.form['expense_date'], '%Y-%m-%d').date(),
                supplier_name=request.form.get('supplier_name'),
                invoice_number=request.form.get('invoice_number'),
                payment_method=request.form.get('payment_method', 'cash'),
                notes=request.form.get('notes'),
                created_by=current_user.id
            )

            db.session.add(expense)
            db.session.commit()
            flash('تم إضافة المصروف بنجاح', 'success')
            return redirect(url_for('expenses_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('finance/add_expense.html')

@app.route('/expenses/<int:expense_id>')
@login_required
def expense_details(expense_id):
    """عرض تفاصيل المصروف"""
    expense = Expense.query.get_or_404(expense_id)
    return render_template('finance/expense_details.html', expense=expense)

@app.route('/expenses/<int:expense_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_expense(expense_id):
    """تعديل المصروف"""
    expense = Expense.query.get_or_404(expense_id)

    if request.method == 'POST':
        try:
            expense.category = request.form['category']
            expense.subcategory = request.form.get('subcategory')
            expense.description = request.form['description']
            expense.amount_jod = float(request.form['amount_jod'])
            expense.quantity = float(request.form['quantity']) if request.form.get('quantity') else None
            expense.unit_price_jod = float(request.form['unit_price_jod']) if request.form.get('unit_price_jod') else None
            expense.unit = request.form.get('unit')
            expense.expense_date = datetime.strptime(request.form['expense_date'], '%Y-%m-%d').date()
            expense.supplier_name = request.form.get('supplier_name')
            expense.invoice_number = request.form.get('invoice_number')
            expense.payment_method = request.form.get('payment_method', 'cash')
            expense.notes = request.form.get('notes')

            db.session.commit()
            flash('تم تحديث المصروف بنجاح', 'success')
            return redirect(url_for('expenses_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('finance/edit_expense.html', expense=expense)

@app.route('/expenses/<int:expense_id>/delete', methods=['POST'])
@login_required
def delete_expense(expense_id):
    """حذف المصروف"""
    expense = Expense.query.get_or_404(expense_id)

    try:
        db.session.delete(expense)
        db.session.commit()
        flash('تم حذف المصروف بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('expenses_list'))

@app.route('/revenues')
@login_required
def revenues_list():
    """عرض قائمة الإيرادات"""
    page = request.args.get('page', 1, type=int)
    source_filter = request.args.get('source', '', type=str)
    date_from = request.args.get('date_from', '', type=str)
    date_to = request.args.get('date_to', '', type=str)

    query = Revenue.query

    if source_filter:
        query = query.filter(Revenue.source == source_filter)

    if date_from:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        query = query.filter(Revenue.sale_date >= from_date)

    if date_to:
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        query = query.filter(Revenue.sale_date <= to_date)

    revenues = query.order_by(Revenue.sale_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # إحصائيات سريعة
    today = date.today()
    month_start = today.replace(day=1)

    total_revenues = db.session.query(db.func.sum(Revenue.amount_jod)).scalar() or 0
    month_revenues = db.session.query(db.func.sum(Revenue.amount_jod)).filter(
        Revenue.sale_date >= month_start
    ).scalar() or 0

    milk_revenues = db.session.query(db.func.sum(Revenue.amount_jod)).filter(
        Revenue.source == 'milk_sale',
        Revenue.sale_date >= month_start
    ).scalar() or 0

    cow_revenues = db.session.query(db.func.sum(Revenue.amount_jod)).filter(
        Revenue.source == 'cow_sale',
        Revenue.sale_date >= month_start
    ).scalar() or 0

    return render_template('finance/revenues_list.html',
                         revenues=revenues,
                         source_filter=source_filter,
                         date_from=date_from,
                         date_to=date_to,
                         total_revenues=total_revenues,
                         month_revenues=month_revenues,
                         milk_revenues=milk_revenues,
                         cow_revenues=cow_revenues)

@app.route('/revenues/add', methods=['GET', 'POST'])
@login_required
def add_revenue():
    """إضافة إيراد جديد"""
    if request.method == 'POST':
        try:
            revenue = Revenue(
                source=request.form['source'],
                description=request.form['description'],
                amount_jod=float(request.form['amount_jod']),
                quantity_liters=float(request.form['quantity_liters']) if request.form.get('quantity_liters') else None,
                price_per_liter_jod=float(request.form['price_per_liter_jod']) if request.form.get('price_per_liter_jod') else None,
                sale_date=datetime.strptime(request.form['sale_date'], '%Y-%m-%d').date(),
                customer_name=request.form.get('customer_name'),
                customer_phone=request.form.get('customer_phone'),
                payment_method=request.form.get('payment_method', 'cash'),
                payment_status=request.form.get('payment_status', 'paid'),
                notes=request.form.get('notes'),
                created_by=current_user.id
            )

            db.session.add(revenue)
            db.session.commit()
            flash('تم إضافة الإيراد بنجاح', 'success')
            return redirect(url_for('revenues_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('finance/add_revenue.html')

@app.route('/revenues/<int:revenue_id>')
@login_required
def revenue_details(revenue_id):
    """عرض تفاصيل الإيراد"""
    revenue = Revenue.query.get_or_404(revenue_id)
    return render_template('finance/revenue_details.html', revenue=revenue)

@app.route('/revenues/<int:revenue_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_revenue(revenue_id):
    """تعديل الإيراد"""
    revenue = Revenue.query.get_or_404(revenue_id)

    if request.method == 'POST':
        try:
            revenue.source = request.form['source']
            revenue.description = request.form['description']
            revenue.amount_jod = float(request.form['amount_jod'])
            revenue.quantity_liters = float(request.form['quantity_liters']) if request.form.get('quantity_liters') else None
            revenue.price_per_liter_jod = float(request.form['price_per_liter_jod']) if request.form.get('price_per_liter_jod') else None
            revenue.sale_date = datetime.strptime(request.form['sale_date'], '%Y-%m-%d').date()
            revenue.customer_name = request.form.get('customer_name')
            revenue.customer_phone = request.form.get('customer_phone')
            revenue.payment_method = request.form.get('payment_method', 'cash')
            revenue.payment_status = request.form.get('payment_status', 'paid')
            revenue.notes = request.form.get('notes')

            db.session.commit()
            flash('تم تحديث الإيراد بنجاح', 'success')
            return redirect(url_for('revenues_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('finance/edit_revenue.html', revenue=revenue)

@app.route('/revenues/<int:revenue_id>/delete', methods=['POST'])
@login_required
def delete_revenue(revenue_id):
    """حذف الإيراد"""
    revenue = Revenue.query.get_or_404(revenue_id)

    try:
        db.session.delete(revenue)
        db.session.commit()
        flash('تم حذف الإيراد بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'error')

    return redirect(url_for('revenues_list'))

# إنشاء قاعدة البيانات والمستخدم الافتراضي
def create_default_data():
    """إنشاء البيانات الافتراضية"""
    db.create_all()

    # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
    if not User.query.first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            full_name='مدير النظام'
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.commit()
        print("تم إنشاء المستخدم الافتراضي: admin / admin123")

    # إضافة بيانات تجريبية للأبقار إذا لم تكن موجودة
    if not Cow.query.first():
        sample_cows = [
            {
                'tag_number': '001',
                'name': 'فاطمة',
                'breed': 'Holstein',
                'birth_date': date(2020, 3, 15),
                'purchase_date': date(2021, 1, 10),
                'purchase_price_jod': 1200.0,
                'current_weight_kg': 550.0,
                'status': 'active',
                'total_calvings': 2
            },
            {
                'tag_number': '002',
                'name': 'عائشة',
                'breed': 'Jersey',
                'birth_date': date(2019, 8, 22),
                'purchase_date': date(2020, 6, 5),
                'purchase_price_jod': 1100.0,
                'current_weight_kg': 480.0,
                'status': 'pregnant',
                'total_calvings': 3
            },
            {
                'tag_number': '003',
                'name': 'خديجة',
                'breed': 'Holstein',
                'birth_date': date(2021, 1, 8),
                'purchase_date': date(2021, 12, 20),
                'purchase_price_jod': 1350.0,
                'current_weight_kg': 520.0,
                'status': 'active',
                'total_calvings': 1
            }
        ]

        for cow_data in sample_cows:
            cow = Cow(**cow_data)
            db.session.add(cow)

        db.session.commit()
        print("تم إنشاء بيانات تجريبية للأبقار")

    # إضافة بيانات تجريبية لإنتاج الحليب
    if not MilkProduction.query.first():
        cows = Cow.query.all()
        if cows:
            today = date.today()
            for i in range(7):  # آخر 7 أيام
                production_date = today - timedelta(days=i)
                for cow in cows:
                    if cow.status == 'active':
                        # إنتاج عشوائي واقعي
                        morning_qty = 12.0 + (i * 0.5)  # تدرج في الإنتاج
                        evening_qty = 10.0 + (i * 0.3)

                        production = MilkProduction(
                            cow_id=cow.id,
                            production_date=production_date,
                            morning_quantity_liters=morning_qty,
                            evening_quantity_liters=evening_qty,
                            total_quantity_liters=morning_qty + evening_qty,
                            fat_percentage=3.5,
                            protein_percentage=3.2,
                            quality_grade='A',
                            milker_name='أحمد محمد'
                        )
                        db.session.add(production)

            db.session.commit()
            print("تم إنشاء بيانات تجريبية لإنتاج الحليب")

    # إضافة بيانات تجريبية لمكونات العلف
    if not FeedComponent.query.first():
        sample_components = [
            {
                'name': 'شعير',
                'name_english': 'Barley',
                'category': 'concentrate',
                'protein_percentage': 12.0,
                'energy_mcal_kg': 2.8,
                'fiber_percentage': 6.0,
                'fat_percentage': 2.5,
                'cost_per_kg_jod': 0.350,
                'supplier': 'مؤسسة الأعلاف الأردنية',
                'supplier_phone': '06-5555555',
                'description': 'شعير عالي الجودة للأبقار الحلوب'
            },
            {
                'name': 'ذرة صفراء',
                'name_english': 'Yellow Corn',
                'category': 'concentrate',
                'protein_percentage': 9.0,
                'energy_mcal_kg': 3.2,
                'fiber_percentage': 3.5,
                'fat_percentage': 4.0,
                'cost_per_kg_jod': 0.380,
                'supplier': 'مؤسسة الأعلاف الأردنية',
                'supplier_phone': '06-5555555',
                'description': 'ذرة صفراء مصدر ممتاز للطاقة'
            },
            {
                'name': 'كسبة فول الصويا',
                'name_english': 'Soybean Meal',
                'category': 'supplement',
                'protein_percentage': 44.0,
                'energy_mcal_kg': 3.3,
                'fiber_percentage': 7.0,
                'fat_percentage': 1.5,
                'cost_per_kg_jod': 0.650,
                'supplier': 'شركة البروتين المتقدم',
                'supplier_phone': '06-4444444',
                'description': 'مصدر بروتين عالي الجودة'
            },
            {
                'name': 'برسيم حجازي',
                'name_english': 'Alfalfa Hay',
                'category': 'roughage',
                'protein_percentage': 18.0,
                'energy_mcal_kg': 2.2,
                'fiber_percentage': 32.0,
                'fat_percentage': 2.0,
                'cost_per_kg_jod': 0.280,
                'supplier': 'مزارع الأردن الخضراء',
                'supplier_phone': '06-3333333',
                'description': 'برسيم حجازي عالي الجودة'
            },
            {
                'name': 'تبن قمح',
                'name_english': 'Wheat Straw',
                'category': 'roughage',
                'protein_percentage': 4.0,
                'energy_mcal_kg': 1.8,
                'fiber_percentage': 42.0,
                'fat_percentage': 1.0,
                'cost_per_kg_jod': 0.120,
                'supplier': 'مزارع الأردن الخضراء',
                'supplier_phone': '06-3333333',
                'description': 'تبن قمح للألياف'
            }
        ]

        for comp_data in sample_components:
            component = FeedComponent(**comp_data)
            db.session.add(component)

        db.session.commit()
        print("تم إنشاء بيانات تجريبية لمكونات العلف")

    # إضافة بيانات تجريبية للسجلات الصحية
    if not HealthRecord.query.first():
        cows = Cow.query.all()
        if cows:
            sample_records = [
                {
                    'cow_id': cows[0].id,
                    'record_date': date.today() - timedelta(days=30),
                    'record_type': 'vaccination',
                    'condition_diagnosis': 'تطعيم ضد الحمى القلاعية',
                    'treatment_given': 'حقن لقاح تحت الجلد',
                    'medication_used': 'لقاح الحمى القلاعية',
                    'dosage': '2 مل',
                    'veterinarian_name': 'د. أحمد محمد',
                    'treatment_cost_jod': 15.00
                },
                {
                    'cow_id': cows[1].id,
                    'record_date': date.today() - timedelta(days=15),
                    'record_type': 'treatment',
                    'condition_diagnosis': 'التهاب الضرع',
                    'treatment_given': 'مضاد حيوي + مضاد التهاب',
                    'medication_used': 'بنسلين + كورتيزون',
                    'dosage': '10 مل يومياً لمدة 5 أيام',
                    'veterinarian_name': 'د. فاطمة علي',
                    'treatment_cost_jod': 45.00,
                    'follow_up_required': True,
                    'follow_up_date': date.today() + timedelta(days=7)
                },
                {
                    'cow_id': cows[2].id,
                    'record_date': date.today() - timedelta(days=7),
                    'record_type': 'checkup',
                    'condition_diagnosis': 'فحص الحمل',
                    'treatment_given': 'فحص بالموجات فوق الصوتية',
                    'veterinarian_name': 'د. محمد خالد',
                    'treatment_cost_jod': 25.00,
                    'notes': 'النتيجة إيجابية - الحمل في الشهر الثالث'
                }
            ]

            for record_data in sample_records:
                record = HealthRecord(**record_data)
                db.session.add(record)

            db.session.commit()
            print("تم إنشاء بيانات تجريبية للسجلات الصحية")

    # إضافة بيانات تجريبية للمصروفات
    if not Expense.query.first():
        sample_expenses = [
            {
                'category': 'feed',
                'subcategory': 'شعير',
                'description': 'شراء شعير للأبقار الحلوب',
                'amount_jod': 350.00,
                'quantity': 1000.0,
                'unit_price_jod': 0.350,
                'unit': 'kg',
                'expense_date': date.today() - timedelta(days=5),
                'supplier_name': 'مؤسسة الأعلاف الأردنية',
                'invoice_number': 'INV-2024-001',
                'payment_method': 'cash',
                'created_by': 1
            },
            {
                'category': 'veterinary',
                'subcategory': 'أدوية',
                'description': 'شراء مضادات حيوية وأدوية بيطرية',
                'amount_jod': 125.50,
                'expense_date': date.today() - timedelta(days=10),
                'supplier_name': 'صيدلية الحيوان',
                'invoice_number': 'VET-2024-015',
                'payment_method': 'bank_transfer',
                'created_by': 1
            },
            {
                'category': 'utilities',
                'subcategory': 'كهرباء',
                'description': 'فاتورة الكهرباء الشهرية',
                'amount_jod': 180.00,
                'expense_date': date.today() - timedelta(days=15),
                'supplier_name': 'شركة الكهرباء الوطنية',
                'invoice_number': 'ELEC-2024-03',
                'payment_method': 'bank_transfer',
                'created_by': 1
            },
            {
                'category': 'labor',
                'subcategory': 'راتب شهري',
                'description': 'راتب عامل المزرعة الشهري',
                'amount_jod': 400.00,
                'expense_date': date.today() - timedelta(days=20),
                'payment_method': 'cash',
                'created_by': 1
            }
        ]

        for expense_data in sample_expenses:
            expense = Expense(**expense_data)
            db.session.add(expense)

        db.session.commit()
        print("تم إنشاء بيانات تجريبية للمصروفات")

    # إضافة بيانات تجريبية للإيرادات
    if not Revenue.query.first():
        sample_revenues = [
            {
                'source': 'milk_sale',
                'description': 'بيع حليب يومي لمصنع الألبان',
                'amount_jod': 45.00,
                'quantity_liters': 150.0,
                'price_per_liter_jod': 0.300,
                'sale_date': date.today() - timedelta(days=1),
                'customer_name': 'مصنع الألبان الأردني',
                'customer_phone': '06-5555555',
                'payment_method': 'bank_transfer',
                'payment_status': 'paid',
                'created_by': 1
            },
            {
                'source': 'milk_sale',
                'description': 'بيع حليب للمستهلكين المحليين',
                'amount_jod': 30.00,
                'quantity_liters': 100.0,
                'price_per_liter_jod': 0.300,
                'sale_date': date.today() - timedelta(days=2),
                'customer_name': 'عملاء محليون',
                'payment_method': 'cash',
                'payment_status': 'paid',
                'created_by': 1
            },
            {
                'source': 'cow_sale',
                'description': 'بيع بقرة حلوب عمر 5 سنوات',
                'amount_jod': 1200.00,
                'sale_date': date.today() - timedelta(days=30),
                'customer_name': 'مزرعة الخير',
                'customer_phone': '079-1234567',
                'payment_method': 'cash',
                'payment_status': 'paid',
                'created_by': 1
            },
            {
                'source': 'calf_sale',
                'description': 'بيع عجل ذكر عمر 6 أشهر',
                'amount_jod': 350.00,
                'sale_date': date.today() - timedelta(days=45),
                'customer_name': 'مزرعة التسمين',
                'customer_phone': '079-7654321',
                'payment_method': 'cash',
                'payment_status': 'paid',
                'created_by': 1
            }
        ]

        for revenue_data in sample_revenues:
            revenue = Revenue(**revenue_data)
            db.session.add(revenue)

        db.session.commit()
        print("تم إنشاء بيانات تجريبية للإيرادات")

if __name__ == '__main__':
    with app.app_context():
        create_default_data()

    print("تم تشغيل تطبيق إدارة مزرعة الألبان")
    print("يمكنك الوصول للتطبيق على: http://localhost:5000")
    print("المستخدم الافتراضي: admin")
    print("كلمة المرور الافتراضية: admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)
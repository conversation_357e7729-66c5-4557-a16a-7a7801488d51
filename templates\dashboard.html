{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة مزرعة الألبان{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: transform 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-5px);
    }
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 15px;
    }
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    .stats-label {
        color: #666;
        font-size: 14px;
        margin-bottom: 0;
    }
    .milk-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .cow-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .production-icon { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .week-icon { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
    .month-icon { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
    .avg-icon { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
    .profit-icon { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
    .loss-icon { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>
                لوحة التحكم الرئيسية
            </h2>
            <p class="text-muted mb-0">نظرة عامة على أداء المزرعة</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة بقرة
                </a>
                <a href="{{ url_for('add_milk_production') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    تسجيل إنتاج
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <!-- Today's Milk Production -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon milk-icon">
                <i class="fas fa-glass-whiskey"></i>
            </div>
            <div class="stats-number">{{ "%.1f"|format(today_milk) }}</div>
            <p class="stats-label">إنتاج اليوم (لتر)</p>
        </div>
    </div>

    <!-- Week's Milk Production -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon week-icon">
                <i class="fas fa-calendar-week"></i>
            </div>
            <div class="stats-number">{{ "%.1f"|format(week_milk) }}</div>
            <p class="stats-label">إنتاج الأسبوع (لتر)</p>
        </div>
    </div>

    <!-- Month's Milk Production -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon month-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-number">{{ "%.1f"|format(month_milk) }}</div>
            <p class="stats-label">إنتاج الشهر (لتر)</p>
        </div>
    </div>

    <!-- Total Cows -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon cow-icon">
                <i class="fas fa-cow"></i>
            </div>
            <div class="stats-number">{{ total_cows }}</div>
            <p class="stats-label">إجمالي الأبقار</p>
        </div>
    </div>

    <!-- Active Cows -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon production-icon">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stats-number">{{ active_cows }}</div>
            <p class="stats-label">الأبقار النشطة</p>
        </div>
    </div>

    <!-- Average Daily Production -->
    <div class="col-lg-4 col-md-6">
        <div class="stats-card">
            <div class="stats-icon avg-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <div class="stats-number">{{ "%.1f"|format(avg_daily_production) }}</div>
            <p class="stats-label">متوسط الإنتاج اليومي (لتر)</p>
        </div>
    </div>
</div>

<!-- Financial Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-money-bill-wave me-2"></i>
            الإحصائيات المالية - الشهر الحالي
        </h4>
    </div>

    <!-- Monthly Revenues -->
    <div class="col-lg-3 col-md-6">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                <i class="fas fa-plus-circle"></i>
            </div>
            <div class="stats-number text-success">{{ "%.0f"|format(month_revenues) }}</div>
            <p class="stats-label">إيرادات الشهر (د.أ)</p>
        </div>
    </div>

    <!-- Monthly Expenses -->
    <div class="col-lg-3 col-md-6">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #dc3545, #fd7e14);">
                <i class="fas fa-minus-circle"></i>
            </div>
            <div class="stats-number text-danger">{{ "%.0f"|format(month_expenses) }}</div>
            <p class="stats-label">مصروفات الشهر (د.أ)</p>
        </div>
    </div>

    <!-- Net Profit -->
    <div class="col-lg-3 col-md-6">
        <div class="stats-card">
            <div class="stats-icon {% if net_profit >= 0 %}profit-icon{% else %}loss-icon{% endif %}">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-number {% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                {{ "%.0f"|format(net_profit) }}
            </div>
            <p class="stats-label">صافي الربح (د.أ)</p>
        </div>
    </div>

    <!-- Profit Margin -->
    <div class="col-lg-3 col-md-6">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stats-number">
                {% if month_revenues > 0 %}
                    {{ "%.1f"|format((net_profit / month_revenues) * 100) }}%
                {% else %}
                    0.0%
                {% endif %}
            </div>
            <p class="stats-label">هامش الربح (%)</p>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-bolt me-2"></i>
                إجراءات سريعة
            </h5>
            <div class="d-grid gap-2">
                <a href="{{ url_for('add_milk_production') }}" class="btn btn-outline-primary">
                    <i class="fas fa-plus me-2"></i>
                    تسجيل إنتاج حليب جديد
                </a>
                <a href="{{ url_for('cows_list') }}" class="btn btn-outline-success">
                    <i class="fas fa-cow me-2"></i>
                    عرض قائمة الأبقار
                </a>
                <a href="{{ url_for('add_health_record') }}" class="btn btn-outline-warning">
                    <i class="fas fa-heartbeat me-2"></i>
                    إضافة سجل صحي
                </a>
                <a href="{{ url_for('feed_components_list') }}" class="btn btn-outline-info">
                    <i class="fas fa-seedling me-2"></i>
                    إدارة العلف
                </a>
                <a href="{{ url_for('add_expense') }}" class="btn btn-outline-danger">
                    <i class="fas fa-minus-circle me-2"></i>
                    إضافة مصروف
                </a>
                <a href="{{ url_for('add_revenue') }}" class="btn btn-outline-success">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة إيراد
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="stats-card">
            <h5 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                معلومات النظام
            </h5>
            <p class="text-muted mb-2">
                نظام إدارة مزرعة الألبان يساعدك في:
            </p>
            <ul class="list-unstyled text-muted">
                <li><i class="fas fa-check text-success me-2"></i>تتبع إنتاج الحليب</li>
                <li><i class="fas fa-check text-success me-2"></i>إدارة الأبقار</li>
                <li><i class="fas fa-check text-success me-2"></i>متابعة الصحة</li>
                <li><i class="fas fa-check text-success me-2"></i>إدارة التغذية</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

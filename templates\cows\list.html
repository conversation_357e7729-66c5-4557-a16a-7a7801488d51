{% extends "base.html" %}

{% block title %}قائمة الأبقار - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-cow me-2"></i>
                إدارة الأبقار
            </h2>
            <p class="text-muted mb-0">إدارة وتتبع جميع الأبقار في المزرعة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة بقرة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ total_cows }}</div>
            <div class="label">إجمالي الأبقار</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ active_cows }}</div>
            <div class="label">أبقار نشطة</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ pregnant_cows }}</div>
            <div class="label">أبقار حامل</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-mini">
            <div class="number">{{ dry_cows }}</div>
            <div class="label">أبقار جافة</div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="content-card">
    <form method="GET" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">البحث</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search }}" placeholder="رقم الأذن، الاسم، أو السلالة">
        </div>
        <div class="col-md-3">
            <label for="status" class="form-label">الحالة</label>
            <select class="form-select" id="status" name="status">
                <option value="">جميع الحالات</option>
                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشطة</option>
                <option value="pregnant" {% if status_filter == 'pregnant' %}selected{% endif %}>حامل</option>
                <option value="dry" {% if status_filter == 'dry' %}selected{% endif %}>جافة</option>
                <option value="sick" {% if status_filter == 'sick' %}selected{% endif %}>مريضة</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{{ url_for('cows_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- Cows Table -->
<div class="content-card">
    {% if cows.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الأذن</th>
                        <th>الاسم</th>
                        <th>السلالة</th>
                        <th>العمر</th>
                        <th>الحالة</th>
                        <th>الوزن الحالي</th>
                        <th>عدد الولادات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cow in cows.items %}
                    <tr>
                        <td>
                            <strong>{{ cow.tag_number }}</strong>
                        </td>
                        <td>{{ cow.name or '-' }}</td>
                        <td>{{ cow.breed or '-' }}</td>
                        <td>{{ cow.birth_date | age_from_birth }}</td>
                        <td>
                            {% if cow.status == 'active' %}
                                <span class="badge bg-success">نشطة</span>
                            {% elif cow.status == 'pregnant' %}
                                <span class="badge bg-info">حامل</span>
                            {% elif cow.status == 'dry' %}
                                <span class="badge bg-warning">جافة</span>
                            {% elif cow.status == 'sick' %}
                                <span class="badge bg-danger">مريضة</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ cow.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if cow.current_weight_kg %}
                                {{ cow.current_weight_kg }} كغ
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ cow.total_calvings or 0 }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('cow_details', cow_id=cow.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_cow', cow_id=cow.id) }}"
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" title="حذف"
                                        onclick="confirmDelete('{{ cow.tag_number }}', '{{ url_for('delete_cow', cow_id=cow.id) }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if cows.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if cows.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('cows_list', page=cows.prev_num, search=search, status=status_filter) }}">السابق</a>
                    </li>
                {% endif %}
                
                {% for page_num in cows.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != cows.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('cows_list', page=page_num, search=search, status=status_filter) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if cows.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('cows_list', page=cows.next_num, search=search, status=status_filter) }}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-cow fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أبقار</h5>
            <p class="text-muted">ابدأ بإضافة أول بقرة في المزرعة</p>
            <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة بقرة جديدة
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(itemName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف البقرة "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // إضافة CSRF token إذا كان متاحاً
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}تعديل الإيراد - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل الإيراد
            </h2>
            <p class="text-muted mb-0">تحديث بيانات {{ revenue.description[:50] }}...</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('revenues_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Edit Revenue Form -->
<div class="content-card">
    <form method="POST" id="revenueForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="source" class="form-label">مصدر الإيراد <span class="text-danger">*</span></label>
                    <select class="form-select" id="source" name="source" required>
                        <option value="">اختر مصدر الإيراد</option>
                        <option value="milk_sale" {% if revenue.source == 'milk_sale' %}selected{% endif %}>بيع حليب</option>
                        <option value="cow_sale" {% if revenue.source == 'cow_sale' %}selected{% endif %}>بيع أبقار</option>
                        <option value="calf_sale" {% if revenue.source == 'calf_sale' %}selected{% endif %}>بيع عجول</option>
                        <option value="manure_sale" {% if revenue.source == 'manure_sale' %}selected{% endif %}>بيع سماد</option>
                        <option value="breeding_service" {% if revenue.source == 'breeding_service' %}selected{% endif %}>خدمات التلقيح</option>
                        <option value="consultation" {% if revenue.source == 'consultation' %}selected{% endif %}>استشارات</option>
                        <option value="other" {% if revenue.source == 'other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="2" 
                              placeholder="وصف تفصيلي للإيراد" required>{{ revenue.description }}</textarea>
                </div>
                
                <div class="mb-3">
                    <label for="sale_date" class="form-label">تاريخ البيع <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="sale_date" name="sale_date" 
                           value="{{ revenue.sale_date.strftime('%Y-%m-%d') }}" required>
                </div>
            </div>
            
            <!-- Financial Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    المعلومات المالية
                </h5>
                
                <div class="mb-3">
                    <label for="amount_jod" class="form-label">المبلغ الإجمالي (دينار أردني) <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" class="form-control" id="amount_jod" name="amount_jod" 
                           value="{{ revenue.amount_jod }}" required>
                </div>
                
                <div class="mb-3" id="milkQuantityContainer" {% if revenue.source != 'milk_sale' %}style="display: none;"{% endif %}>
                    <label for="quantity_liters" class="form-label">كمية الحليب (لتر)</label>
                    <input type="number" step="0.1" class="form-control" id="quantity_liters" name="quantity_liters" 
                           value="{{ revenue.quantity_liters or '' }}"
                           placeholder="كمية الحليب المباعة">
                </div>
                
                <div class="mb-3" id="milkPriceContainer" {% if revenue.source != 'milk_sale' %}style="display: none;"{% endif %}>
                    <label for="price_per_liter_jod" class="form-label">سعر اللتر (دينار أردني)</label>
                    <input type="number" step="0.001" class="form-control" id="price_per_liter_jod" name="price_per_liter_jod" 
                           value="{{ revenue.price_per_liter_jod or '' }}"
                           placeholder="سعر اللتر الواحد" readonly>
                </div>
                
                <div class="mb-3">
                    <label for="payment_status" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="paid" {% if revenue.payment_status == 'paid' %}selected{% endif %}>مدفوع</option>
                        <option value="pending" {% if revenue.payment_status == 'pending' %}selected{% endif %}>معلق</option>
                        <option value="overdue" {% if revenue.payment_status == 'overdue' %}selected{% endif %}>متأخر</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Customer Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
                
                <div class="mb-3">
                    <label for="customer_name" class="form-label">اسم العميل</label>
                    <input type="text" class="form-control" id="customer_name" name="customer_name" 
                           value="{{ revenue.customer_name or '' }}"
                           placeholder="اسم العميل أو الشركة">
                </div>
                
                <div class="mb-3">
                    <label for="customer_phone" class="form-label">هاتف العميل</label>
                    <input type="text" class="form-control" id="customer_phone" name="customer_phone" 
                           value="{{ revenue.customer_phone or '' }}"
                           placeholder="رقم هاتف العميل">
                </div>
            </div>
            
            <!-- Payment Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    معلومات الدفع
                </h5>
                
                <div class="mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="cash" {% if revenue.payment_method == 'cash' %}selected{% endif %}>نقدي</option>
                        <option value="bank_transfer" {% if revenue.payment_method == 'bank_transfer' %}selected{% endif %}>تحويل بنكي</option>
                        <option value="check" {% if revenue.payment_method == 'check' %}selected{% endif %}>شيك</option>
                        <option value="credit_card" {% if revenue.payment_method == 'credit_card' %}selected{% endif %}>بطاقة ائتمان</option>
                        <option value="debit_card" {% if revenue.payment_method == 'debit_card' %}selected{% endif %}>بطاقة خصم</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية حول الإيراد">{{ revenue.notes or '' }}</textarea>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('revenues_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide milk-specific fields based on source
    const sourceSelect = document.getElementById('source');
    const milkQuantityContainer = document.getElementById('milkQuantityContainer');
    const milkPriceContainer = document.getElementById('milkPriceContainer');
    
    sourceSelect.addEventListener('change', function() {
        if (this.value === 'milk_sale') {
            milkQuantityContainer.style.display = 'block';
            milkPriceContainer.style.display = 'block';
        } else {
            milkQuantityContainer.style.display = 'none';
            milkPriceContainer.style.display = 'none';
            document.getElementById('quantity_liters').value = '';
            document.getElementById('price_per_liter_jod').value = '';
        }
    });
    
    // Calculate price per liter automatically
    function calculatePricePerLiter() {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const quantity = parseFloat(document.getElementById('quantity_liters').value) || 0;
        const pricePerLiterInput = document.getElementById('price_per_liter_jod');
        
        if (amount > 0 && quantity > 0) {
            const pricePerLiter = amount / quantity;
            pricePerLiterInput.value = pricePerLiter.toFixed(3);
        } else {
            pricePerLiterInput.value = '';
        }
    }
    
    // Add event listeners for automatic calculation
    document.getElementById('amount_jod').addEventListener('input', calculatePricePerLiter);
    document.getElementById('quantity_liters').addEventListener('input', calculatePricePerLiter);
    
    // Form validation
    document.getElementById('revenueForm').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const saleDate = new Date(document.getElementById('sale_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (amount <= 0) {
            alert('المبلغ يجب أن يكون أكبر من صفر');
            e.preventDefault();
            return false;
        }
        
        if (saleDate > today) {
            alert('لا يمكن تسجيل إيراد لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
        
        const source = document.getElementById('source').value;
        const quantity = parseFloat(document.getElementById('quantity_liters').value) || 0;
        
        if (source === 'milk_sale' && quantity <= 0) {
            if (!confirm('لم يتم إدخال كمية الحليب. هل تريد المتابعة؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
    
    // Calculate price per liter on page load
    calculatePricePerLiter();
});
</script>
{% endblock %}

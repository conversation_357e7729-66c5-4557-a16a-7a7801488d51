{% extends "base.html" %}

{% block title %}تعديل البقرة {{ cow.tag_number }} - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                تعديل البقرة {{ cow.tag_number }}
            </h2>
            <p class="text-muted mb-0">تحديث بيانات البقرة</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="{{ url_for('cow_details', cow_id=cow.id) }}" class="btn btn-outline-info">
                    <i class="fas fa-eye me-1"></i>
                    عرض التفاصيل
                </a>
                <a href="{{ url_for('cows_list') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Edit Cow Form -->
<div class="content-card">
    <form method="POST">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="tag_number" class="form-label">رقم الأذن <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="tag_number" name="tag_number" 
                           value="{{ cow.tag_number }}" required>
                </div>
                
                <div class="mb-3">
                    <label for="name" class="form-label">الاسم</label>
                    <input type="text" class="form-control" id="name" name="name" 
                           value="{{ cow.name or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="breed" class="form-label">السلالة</label>
                    <select class="form-select" id="breed" name="breed">
                        <option value="">اختر السلالة</option>
                        <option value="Holstein" {% if cow.breed == 'Holstein' %}selected{% endif %}>هولشتاين</option>
                        <option value="Jersey" {% if cow.breed == 'Jersey' %}selected{% endif %}>جيرسي</option>
                        <option value="Brown Swiss" {% if cow.breed == 'Brown Swiss' %}selected{% endif %}>براون سويس</option>
                        <option value="Guernsey" {% if cow.breed == 'Guernsey' %}selected{% endif %}>جيرنسي</option>
                        <option value="Ayrshire" {% if cow.breed == 'Ayrshire' %}selected{% endif %}>أيرشاير</option>
                        <option value="Mixed" {% if cow.breed == 'Mixed' %}selected{% endif %}>مختلطة</option>
                        <option value="Other" {% if cow.breed == 'Other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="active" {% if cow.status == 'active' %}selected{% endif %}>نشطة</option>
                        <option value="pregnant" {% if cow.status == 'pregnant' %}selected{% endif %}>حامل</option>
                        <option value="dry" {% if cow.status == 'dry' %}selected{% endif %}>جافة</option>
                        <option value="sick" {% if cow.status == 'sick' %}selected{% endif %}>مريضة</option>
                    </select>
                </div>
            </div>
            
            <!-- Dates and Purchase Info -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-calendar me-2"></i>
                    التواريخ ومعلومات الشراء
                </h5>
                
                <div class="mb-3">
                    <label for="birth_date" class="form-label">تاريخ الولادة</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" 
                           value="{{ cow.birth_date.strftime('%Y-%m-%d') if cow.birth_date else '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_date" class="form-label">تاريخ الشراء</label>
                    <input type="date" class="form-control" id="purchase_date" name="purchase_date" 
                           value="{{ cow.purchase_date.strftime('%Y-%m-%d') if cow.purchase_date else '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_price_jod" class="form-label">سعر الشراء (دينار أردني)</label>
                    <input type="number" step="0.01" class="form-control" id="purchase_price_jod" 
                           name="purchase_price_jod" value="{{ cow.purchase_price_jod or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="purchase_weight_kg" class="form-label">وزن الشراء (كغ)</label>
                    <input type="number" step="0.1" class="form-control" id="purchase_weight_kg" 
                           name="purchase_weight_kg" value="{{ cow.purchase_weight_kg or '' }}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Physical Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-weight me-2"></i>
                    المعلومات الجسدية
                </h5>
                
                <div class="mb-3">
                    <label for="current_weight_kg" class="form-label">الوزن الحالي (كغ)</label>
                    <input type="number" step="0.1" class="form-control" id="current_weight_kg" 
                           name="current_weight_kg" value="{{ cow.current_weight_kg or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="body_condition_score" class="form-label">درجة الحالة الجسدية (1-5)</label>
                    <input type="number" step="0.1" min="1" max="5" class="form-control" 
                           id="body_condition_score" name="body_condition_score" 
                           value="{{ cow.body_condition_score or '' }}">
                    <div class="form-text">1 = نحيفة جداً، 5 = سمينة جداً</div>
                </div>
            </div>
            
            <!-- Breeding Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-heart me-2"></i>
                    معلومات التناسل
                </h5>
                
                <div class="mb-3">
                    <label for="last_breeding_date" class="form-label">تاريخ آخر تلقيح</label>
                    <input type="date" class="form-control" id="last_breeding_date" 
                           name="last_breeding_date" 
                           value="{{ cow.last_breeding_date.strftime('%Y-%m-%d') if cow.last_breeding_date else '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="expected_calving_date" class="form-label">تاريخ الولادة المتوقع</label>
                    <input type="date" class="form-control" id="expected_calving_date" 
                           name="expected_calving_date" 
                           value="{{ cow.expected_calving_date.strftime('%Y-%m-%d') if cow.expected_calving_date else '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="total_calvings" class="form-label">عدد الولادات الإجمالي</label>
                    <input type="number" min="0" class="form-control" id="total_calvings" 
                           name="total_calvings" value="{{ cow.total_calvings or 0 }}">
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Genetic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-dna me-2"></i>
                    المعلومات الوراثية
                </h5>
                
                <div class="mb-3">
                    <label for="sire_name" class="form-label">اسم الأب</label>
                    <input type="text" class="form-control" id="sire_name" name="sire_name" 
                           value="{{ cow.sire_name or '' }}">
                </div>
                
                <div class="mb-3">
                    <label for="dam_name" class="form-label">اسم الأم</label>
                    <input type="text" class="form-control" id="dam_name" name="dam_name" 
                           value="{{ cow.dam_name or '' }}">
                </div>
            </div>
            
            <!-- Notes -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="4" 
                              placeholder="أي ملاحظات إضافية حول البقرة">{{ cow.notes or '' }}</textarea>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('cow_details', cow_id=cow.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate expected calving date when breeding date changes
    document.getElementById('last_breeding_date').addEventListener('change', function() {
        const breedingDate = new Date(this.value);
        if (breedingDate) {
            // Add 280 days (average gestation period for cows)
            const calvingDate = new Date(breedingDate);
            calvingDate.setDate(calvingDate.getDate() + 280);
            
            document.getElementById('expected_calving_date').value = calvingDate.toISOString().split('T')[0];
        }
    });
    
    // Update status to pregnant if expected calving date is set
    document.getElementById('expected_calving_date').addEventListener('change', function() {
        if (this.value) {
            const statusSelect = document.getElementById('status');
            if (statusSelect.value === 'active') {
                statusSelect.value = 'pregnant';
            }
        }
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const birthDate = new Date(document.getElementById('birth_date').value);
        const purchaseDate = new Date(document.getElementById('purchase_date').value);
        
        if (birthDate && purchaseDate && birthDate > purchaseDate) {
            alert('تاريخ الولادة يجب أن يكون قبل تاريخ الشراء');
            e.preventDefault();
            return false;
        }
        
        const breedingDate = new Date(document.getElementById('last_breeding_date').value);
        const calvingDate = new Date(document.getElementById('expected_calving_date').value);
        
        if (breedingDate && calvingDate) {
            const diffDays = (calvingDate - breedingDate) / (1000 * 60 * 60 * 24);
            if (diffDays < 250 || diffDays > 300) {
                if (!confirm('فترة الحمل غير طبيعية (' + Math.round(diffDays) + ' يوم). هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return false;
                }
            }
        }
    });
});
</script>
{% endblock %}

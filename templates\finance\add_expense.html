{% extends "base.html" %}

{% block title %}إضافة مصروف جديد - نظام إدارة مزرعة الألبان{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="mb-0">
                <i class="fas fa-plus me-2"></i>
                إضافة مصروف جديد
            </h2>
            <p class="text-muted mb-0">تسجيل مصروف جديد للمزرعة</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Add Expense Form -->
<div class="content-card">
    <form method="POST" id="expenseForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
                
                <div class="mb-3">
                    <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="feed">علف</option>
                        <option value="veterinary">بيطري</option>
                        <option value="labor">عمالة</option>
                        <option value="utilities">مرافق (كهرباء، ماء، غاز)</option>
                        <option value="maintenance">صيانة</option>
                        <option value="equipment">معدات</option>
                        <option value="transport">نقل</option>
                        <option value="insurance">تأمين</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="subcategory" class="form-label">الفئة الفرعية</label>
                    <input type="text" class="form-control" id="subcategory" name="subcategory" 
                           placeholder="مثل: شعير، أدوية، راتب">
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="2" 
                              placeholder="وصف تفصيلي للمصروف" required></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="expense_date" name="expense_date" required>
                </div>
            </div>
            
            <!-- Financial Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    المعلومات المالية
                </h5>
                
                <div class="mb-3">
                    <label for="amount_jod" class="form-label">المبلغ الإجمالي (دينار أردني) <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" class="form-control" id="amount_jod" name="amount_jod" required>
                </div>
                
                <div class="mb-3">
                    <label for="quantity" class="form-label">الكمية</label>
                    <input type="number" step="0.1" class="form-control" id="quantity" name="quantity" 
                           placeholder="الكمية المشتراة">
                </div>
                
                <div class="mb-3">
                    <label for="unit" class="form-label">الوحدة</label>
                    <select class="form-select" id="unit" name="unit">
                        <option value="">اختر الوحدة</option>
                        <option value="kg">كيلوغرام</option>
                        <option value="liter">لتر</option>
                        <option value="ton">طن</option>
                        <option value="piece">قطعة</option>
                        <option value="hour">ساعة</option>
                        <option value="day">يوم</option>
                        <option value="month">شهر</option>
                        <option value="service">خدمة</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="unit_price_jod" class="form-label">سعر الوحدة (دينار أردني)</label>
                    <input type="number" step="0.001" class="form-control" id="unit_price_jod" name="unit_price_jod" 
                           placeholder="سعر الوحدة الواحدة" readonly>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Supplier Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-truck me-2"></i>
                    معلومات المورد
                </h5>
                
                <div class="mb-3">
                    <label for="supplier_name" class="form-label">اسم المورد</label>
                    <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                           placeholder="اسم المورد أو الشركة">
                </div>
                
                <div class="mb-3">
                    <label for="invoice_number" class="form-label">رقم الفاتورة</label>
                    <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                           placeholder="رقم الفاتورة أو الإيصال">
                </div>
            </div>
            
            <!-- Payment Information -->
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-credit-card me-2"></i>
                    معلومات الدفع
                </h5>
                
                <div class="mb-3">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="cash">نقدي</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                        <option value="credit_card">بطاقة ائتمان</option>
                        <option value="debit_card">بطاقة خصم</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية حول المصروف"></textarea>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="d-flex justify-content-between">
            <a href="{{ url_for('expenses_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>
                حفظ المصروف
            </button>
        </div>
    </form>
</div>

<!-- Quick Categories Reference -->
<div class="content-card">
    <h5 class="mb-3">
        <i class="fas fa-lightbulb me-2"></i>
        مرجع سريع - فئات المصروفات
    </h5>
    <div class="row">
        <div class="col-md-3">
            <h6><span class="badge bg-success">علف</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• شعير، ذرة، برسيم</li>
                <li>• مكملات غذائية</li>
                <li>• خلطات علفية</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-danger">بيطري</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• أدوية وعلاجات</li>
                <li>• تطعيمات</li>
                <li>• فحوصات طبية</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-primary">عمالة</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• رواتب العمال</li>
                <li>• أجور يومية</li>
                <li>• مكافآت</li>
            </ul>
        </div>
        <div class="col-md-3">
            <h6><span class="badge bg-warning">مرافق</span></h6>
            <ul class="list-unstyled text-muted small">
                <li>• فواتير الكهرباء</li>
                <li>• فواتير الماء</li>
                <li>• الغاز والوقود</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('expense_date').value = today;
    
    // Calculate unit price automatically
    function calculateUnitPrice() {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const unitPriceInput = document.getElementById('unit_price_jod');
        
        if (amount > 0 && quantity > 0) {
            const unitPrice = amount / quantity;
            unitPriceInput.value = unitPrice.toFixed(3);
        } else {
            unitPriceInput.value = '';
        }
    }
    
    // Add event listeners for automatic calculation
    document.getElementById('amount_jod').addEventListener('input', calculateUnitPrice);
    document.getElementById('quantity').addEventListener('input', calculateUnitPrice);
    
    // Auto-suggest subcategories based on category
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const subcategoryInput = document.getElementById('subcategory');
        
        // Clear previous value
        subcategoryInput.value = '';
        subcategoryInput.placeholder = '';
        
        if (category === 'feed') {
            subcategoryInput.placeholder = 'مثل: شعير، ذرة، برسيم، مكملات';
        } else if (category === 'veterinary') {
            subcategoryInput.placeholder = 'مثل: أدوية، تطعيمات، فحوصات';
        } else if (category === 'labor') {
            subcategoryInput.placeholder = 'مثل: راتب شهري، أجر يومي، مكافأة';
        } else if (category === 'utilities') {
            subcategoryInput.placeholder = 'مثل: كهرباء، ماء، غاز، وقود';
        } else if (category === 'maintenance') {
            subcategoryInput.placeholder = 'مثل: صيانة معدات، إصلاحات، قطع غيار';
        } else if (category === 'equipment') {
            subcategoryInput.placeholder = 'مثل: أدوات، آلات، معدات حلب';
        }
    });
    
    // Form validation
    document.getElementById('expenseForm').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('amount_jod').value) || 0;
        const expenseDate = new Date(document.getElementById('expense_date').value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (amount <= 0) {
            alert('المبلغ يجب أن يكون أكبر من صفر');
            e.preventDefault();
            return false;
        }
        
        if (expenseDate > today) {
            alert('لا يمكن تسجيل مصروف لتاريخ مستقبلي');
            e.preventDefault();
            return false;
        }
        
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const unit = document.getElementById('unit').value;
        
        if (quantity > 0 && !unit) {
            if (!confirm('تم إدخال كمية بدون تحديد الوحدة. هل تريد المتابعة؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
{% endblock %}
